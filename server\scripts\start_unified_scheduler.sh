#!/bin/bash
# Unified Data Scheduler Startup Script for Linux/macOS
# 统一数据调度器启动脚本 (Linux/macOS)

echo "Starting Unified Data Scheduler..."
echo "启动统一数据调度器..."

# Change to server directory
cd "$(dirname "$0")/.."

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Error: Python3 is not installed or not in PATH"
    echo "错误：Python3未安装或不在PATH中"
    exit 1
fi

# Default parameters
MODE="monitor"
DATA_TYPES="all"
UID="401315430"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --mode)
            MODE="$2"
            shift 2
            ;;
        --data-types)
            DATA_TYPES="$2"
            shift 2
            ;;
        --uid)
            UID="$2"
            shift 2
            ;;
        --help)
            python3 unified_scheduler_main.py --mode help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

echo "Mode: $MODE"
echo "Data Types: $DATA_TYPES"
echo "UID: $UID"
echo

# Start the scheduler
python3 unified_scheduler_main.py --mode "$MODE" --data-types "$DATA_TYPES" --uid "$UID"

if [ $? -ne 0 ]; then
    echo
    echo "Error: Scheduler failed to start"
    echo "错误：调度器启动失败"
    exit 1
fi

echo
echo "Scheduler finished"
echo "调度器已结束"
