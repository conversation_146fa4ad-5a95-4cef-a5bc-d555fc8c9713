"""
Cookie Adapter Module

Provides cookie adaptation for existing servers to use task-specific cookies
"""

from typing import Optional, Dict
from bilibili_api import Credential
from logger import logger
from .cookie_manager import get_task_cookie


class CookieAdapter:
    """
    Cookie Adapter for existing servers
    现有服务器的Cookie适配器
    """
    
    @staticmethod
    def get_credential_for_task(task_type: str, fallback_credential: Optional[Credential] = None) -> Optional[Credential]:
        """
        Get Credential object for specific task type
        获取特定任务类型的Credential对象
        
        Args:
            task_type: Task type ('user', 'creator', 'live')
            fallback_credential: Fallback credential if task-specific cookie not available
            
        Returns:
            Credential object or None
        """
        try:
            cookie_str = get_task_cookie(task_type)
            if not cookie_str:
                logger.warning(f"No cookie available for task '{task_type}', using fallback")
                return fallback_credential
                
            # Parse cookie string to extract individual values
            cookie_dict = {}
            for item in cookie_str.split('; '):
                if '=' in item:
                    key, value = item.split('=', 1)
                    cookie_dict[key] = value
            
            # Create Credential object
            credential = Credential(
                sessdata=cookie_dict.get('SESSDATA', ''),
                bili_jct=cookie_dict.get('bili_jct', ''),
                buvid3=cookie_dict.get('buvid3', ''),
                dedeuserid=cookie_dict.get('DedeUserID', ''),
                buvid4=cookie_dict.get('buvid4', '')
            )
            
            logger.debug(f"Created credential for task '{task_type}'")
            return credential
            
        except Exception as e:
            logger.error(f"Error creating credential for task '{task_type}': {e}")
            return fallback_credential
    
    @staticmethod
    def get_cookie_dict_for_task(task_type: str) -> Dict[str, str]:
        """
        Get cookie dictionary for specific task type
        获取特定任务类型的Cookie字典
        
        Args:
            task_type: Task type ('user', 'creator', 'live')
            
        Returns:
            Dictionary of cookie key-value pairs
        """
        try:
            cookie_str = get_task_cookie(task_type)
            if not cookie_str:
                return {}
                
            cookie_dict = {}
            for item in cookie_str.split('; '):
                if '=' in item:
                    key, value = item.split('=', 1)
                    cookie_dict[key] = value
            
            return cookie_dict
            
        except Exception as e:
            logger.error(f"Error getting cookie dict for task '{task_type}': {e}")
            return {}
    
    @staticmethod
    def update_server_credential(server, task_type: str):
        """
        Update server's credential with task-specific cookie
        使用任务专属Cookie更新服务器的credential
        
        Args:
            server: Server instance to update
            task_type: Task type ('user', 'creator', 'live')
        """
        try:
            new_credential = CookieAdapter.get_credential_for_task(task_type, server.credential)
            if new_credential and new_credential != server.credential:
                server.credential = new_credential
                # Update related objects that use credential
                if hasattr(server, 'u') and server.u:
                    server.u.credential = new_credential
                if hasattr(server, 'liveroom') and server.liveroom:
                    server.liveroom.credential = new_credential
                logger.info(f"Updated credential for server with task '{task_type}' cookie")
            
        except Exception as e:
            logger.error(f"Error updating server credential for task '{task_type}': {e}")


def apply_user_task_cookies():
    """
    Apply user task cookies to all user servers
    为所有用户服务器应用用户任务Cookie

    Note: This function is now deprecated as cookie management is integrated
    directly into server initialization.
    """
    logger.info("Cookie management is now integrated into server initialization")


def apply_creator_task_cookies(creator_server):
    """
    Apply creator task cookies to creator server
    为创作者服务器应用创作者任务Cookie

    Note: This function is now deprecated as cookie management is integrated
    directly into server initialization.

    Args:
        creator_server: Creator server instance
    """
    logger.info("Cookie management is now integrated into server initialization")
