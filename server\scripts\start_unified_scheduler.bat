@echo off
REM Unified Data Scheduler Startup Script for Windows
REM 统一数据调度器启动脚本 (Windows)

echo Starting Unified Data Scheduler...
echo 启动统一数据调度器...

cd /d "%~dp0\.."

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo 错误：Python未安装或不在PATH中
    pause
    exit /b 1
)

REM Default parameters
set MODE=monitor
set DATA_TYPES=all
set UID=401315430

REM Parse command line arguments
:parse_args
if "%1"=="" goto start_scheduler
if "%1"=="--mode" (
    set MODE=%2
    shift
    shift
    goto parse_args
)
if "%1"=="--data-types" (
    set DATA_TYPES=%2
    shift
    shift
    goto parse_args
)
if "%1"=="--uid" (
    set UID=%2
    shift
    shift
    goto parse_args
)
if "%1"=="--help" (
    python unified_scheduler_main.py --mode help
    pause
    exit /b 0
)
shift
goto parse_args

:start_scheduler
echo Mode: %MODE%
echo Data Types: %DATA_TYPES%
echo UID: %UID%
echo.

REM Start the scheduler
python unified_scheduler_main.py --mode %MODE% --data-types %DATA_TYPES% --uid %UID%

if errorlevel 1 (
    echo.
    echo Error: Scheduler failed to start
    echo 错误：调度器启动失败
    pause
    exit /b 1
)

echo.
echo Scheduler finished
echo 调度器已结束
pause
