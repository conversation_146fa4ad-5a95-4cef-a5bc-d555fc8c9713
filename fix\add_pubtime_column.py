#!/usr/bin/env python3
"""
Database migration script to add pubtime column to video_compare_table.

This script:
1. Adds the pubtime column to video_compare_table if it doesn't exist
2. Sets pubtime to NULL for historical data (before today)
3. Handles today's data appropriately
4. Is idempotent (safe to run multiple times)

Usage:
    python fix/add_pubtime_column.py
"""

import asyncio
from datetime import datetime, timedelta


from backend.utils.db_pool import get_connection
from logger import logger


async def check_column_exists(conn, table_name: str, column_name: str) -> bool:
    """Check if a column exists in the specified table."""
    query = """
        SELECT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = $1 AND column_name = $2
        );
    """
    result = await conn.fetchval(query, table_name, column_name)
    return result


async def add_pubtime_column(conn):
    """Add pubtime column to video_compare_table if it doesn't exist."""
    table_name = "video_compare_table"
    column_name = "pubtime"
    
    # Check if column already exists
    column_exists = await check_column_exists(conn, table_name, column_name)
    
    if column_exists:
        logger.info(f"Column '{column_name}' already exists in '{table_name}'. Skipping addition.")
        return False
    
    # Add the column
    alter_query = f"ALTER TABLE {table_name} ADD COLUMN {column_name} bigint;"
    await conn.execute(alter_query)
    logger.info(f"Successfully added column '{column_name}' to '{table_name}'.")
    return True


async def update_historical_data(conn):
    """Set pubtime to NULL for historical data (before today)."""
    # Get today's start timestamp
    today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    today_timestamp = int(today.timestamp())
    
    # Update records created before today to have NULL pubtime
    update_query = """
        UPDATE video_compare_table 
        SET pubtime = NULL 
        WHERE create_time < $1 AND pubtime IS NOT NULL;
    """
    
    result = await conn.execute(update_query, today_timestamp)
    affected_rows = int(result.split()[-1]) if result.startswith('UPDATE') else 0
    logger.info(f"Set pubtime to NULL for {affected_rows} historical records (before today).")
    return affected_rows


async def handle_todays_data(conn):
    """Handle today's data - keep existing pubtime values if they exist."""
    # Get today's start and end timestamps
    today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    tomorrow = today + timedelta(days=1)
    today_timestamp = int(today.timestamp())
    tomorrow_timestamp = int(tomorrow.timestamp())
    
    # Count today's records
    count_query = """
        SELECT COUNT(*) 
        FROM video_compare_table 
        WHERE create_time >= $1 AND create_time < $2;
    """
    
    count = await conn.fetchval(count_query, today_timestamp, tomorrow_timestamp)
    logger.info(f"Found {count} records for today. Keeping existing pubtime values.")
    return count


async def verify_migration(conn):
    """Verify the migration was successful."""
    # Check if column exists
    column_exists = await check_column_exists(conn, "video_compare_table", "pubtime")
    if not column_exists:
        raise Exception("Migration failed: pubtime column does not exist after migration.")
    
    # Get some statistics
    stats_query = """
        SELECT 
            COUNT(*) as total_records,
            COUNT(pubtime) as records_with_pubtime,
            COUNT(*) - COUNT(pubtime) as records_with_null_pubtime
        FROM video_compare_table;
    """
    
    stats = await conn.fetchrow(stats_query)
    logger.info(f"Migration verification:")
    logger.info(f"  Total records: {stats['total_records']}")
    logger.info(f"  Records with pubtime: {stats['records_with_pubtime']}")
    logger.info(f"  Records with NULL pubtime: {stats['records_with_null_pubtime']}")


async def main():
    """Main migration function."""
    logger.info("Starting pubtime column migration for video_compare_table...")
    
    try:
        async with get_connection() as conn:
            # Start transaction
            async with conn.transaction():
                # Add the column
                column_added = await add_pubtime_column(conn)
                
                if column_added:
                    # Update historical data
                    await update_historical_data(conn)
                    
                    # Handle today's data
                    await handle_todays_data(conn)
                
                # Verify migration
                await verify_migration(conn)
                
                logger.info("Migration completed successfully!")
                
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
