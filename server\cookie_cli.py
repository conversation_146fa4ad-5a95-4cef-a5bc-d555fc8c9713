"""
Cookie Management CLI Tool

Command line interface for managing cookies and refresh tokens
"""

import asyncio
import argparse
import json
from typing import Dict, Optional
from logger import logger
from .cookie_manager import get_cookie_manager
from .cookie_refresh import BilibiliCookieRefresher


async def show_status():
    """Show cookie status for all tasks"""
    manager = get_cookie_manager()
    status = manager.get_status()
    
    print("=== Cookie Status ===")
    print(f"Enabled tasks: {', '.join(status['enabled_tasks'])}")
    print()
    
    for task_type, cookie_status in status['cookies'].items():
        print(f"Task: {task_type}")
        print(f"  Enabled: {cookie_status['enabled']}")
        print(f"  Has refresh token: {cookie_status['has_refresh_token']}")
        print(f"  Last refresh: {cookie_status['last_refresh']}")
        print(f"  Needs refresh: {cookie_status['needs_refresh']}")
        print()


async def set_cookie(task_type: str, cookie_string: str, refresh_token: str = ""):
    """Set cookie for specific task type"""
    manager = get_cookie_manager()
    
    # Parse cookie string
    cookie_dict = {}
    for item in cookie_string.split(';'):
        if '=' in item:
            key, value = item.strip().split('=', 1)
            cookie_dict[key] = value
    
    # Create cookie data
    cookie_data = {
        "description": f"Cookie for {task_type} data collection tasks",
        "SESSDATA": cookie_dict.get('SESSDATA', ''),
        "bili_jct": cookie_dict.get('bili_jct', ''),
        "buvid3": cookie_dict.get('buvid3', ''),
        "buvid4": cookie_dict.get('buvid4', ''),
        "DedeUserID": cookie_dict.get('DedeUserID', ''),
        "b_nut": cookie_dict.get('b_nut', ''),
        "sid": cookie_dict.get('sid', ''),
        "refresh_token": refresh_token,
        "last_refresh": None,
        "enabled": True
    }
    
    manager.update_cookie_data(task_type, cookie_data)
    print(f"Cookie set for task '{task_type}'")


async def enable_task(task_type: str):
    """Enable cookie for specific task type"""
    manager = get_cookie_manager()
    cookie_data = manager.get_cookie_data(task_type)
    
    if not cookie_data:
        print(f"No cookie data found for task '{task_type}'. Please set cookie first.")
        return
    
    cookie_data['enabled'] = True
    manager.update_cookie_data(task_type, cookie_data)
    print(f"Task '{task_type}' enabled")


async def disable_task(task_type: str):
    """Disable cookie for specific task type"""
    manager = get_cookie_manager()
    cookie_data = manager.get_cookie_data(task_type)
    
    if not cookie_data:
        print(f"No cookie data found for task '{task_type}'")
        return
    
    cookie_data['enabled'] = False
    manager.update_cookie_data(task_type, cookie_data)
    print(f"Task '{task_type}' disabled")


async def refresh_cookie(task_type: str):
    """Manually refresh cookie for specific task type"""
    manager = get_cookie_manager()
    
    print(f"Refreshing cookie for task '{task_type}'...")
    success = await manager.refresh_cookie_for_task(task_type)
    
    if success:
        print(f"Cookie refresh successful for task '{task_type}'")
    else:
        print(f"Cookie refresh failed for task '{task_type}'")


async def refresh_all():
    """Refresh all cookies that need refreshing"""
    manager = get_cookie_manager()
    
    print("Checking and refreshing all cookies...")
    await manager.refresh_all_cookies()
    print("Cookie refresh check completed")


async def test_cookie(task_type: str):
    """Test if cookie is valid"""
    manager = get_cookie_manager()
    cookie_str = manager.get_cookie_for_task(task_type)
    
    if not cookie_str:
        print(f"No cookie available for task '{task_type}'")
        return
    
    print(f"Testing cookie for task '{task_type}'...")
    
    async with BilibiliCookieRefresher() as refresher:
        needs_refresh, timestamp = await refresher.check_refresh_needed(cookie_str)
        
    if needs_refresh is not None:
        print(f"Cookie test successful")
        print(f"Needs refresh: {needs_refresh}")
        print(f"Timestamp: {timestamp}")
    else:
        print(f"Cookie test failed")


def print_help():
    """Print help information"""
    help_text = """
Cookie Management CLI Tool - Cookie管理命令行工具

Usage:
    python -m server.cookie_cli [command] [options]

Commands:
    status                          显示所有任务的Cookie状态
    set <task> <cookie> [token]     设置任务的Cookie和刷新令牌
    enable <task>                   启用任务的Cookie
    disable <task>                  禁用任务的Cookie
    refresh <task>                  手动刷新任务的Cookie
    refresh-all                     刷新所有需要刷新的Cookie
    test <task>                     测试任务的Cookie是否有效
    help                           显示帮助信息

Task Types:
    user        用户数据收集任务
    creator     创作者数据收集任务
    live        直播数据收集任务

Examples:
    python -m server.cookie_cli status
    python -m server.cookie_cli set user "SESSDATA=xxx;bili_jct=xxx;..." "refresh_token_here"
    python -m server.cookie_cli enable user
    python -m server.cookie_cli refresh user
    python -m server.cookie_cli test user
    """
    print(help_text)


async def main():
    """Main CLI function"""
    parser = argparse.ArgumentParser(description='Cookie Management CLI Tool')
    parser.add_argument('command', nargs='?', help='Command to execute')
    parser.add_argument('task_type', nargs='?', help='Task type (user/creator/live)')
    parser.add_argument('cookie_string', nargs='?', help='Cookie string')
    parser.add_argument('refresh_token', nargs='?', default='', help='Refresh token')
    
    args = parser.parse_args()
    
    if not args.command or args.command == 'help':
        print_help()
        return
    
    try:
        if args.command == 'status':
            await show_status()
        elif args.command == 'set':
            if not args.task_type or not args.cookie_string:
                print("Error: task_type and cookie_string are required for 'set' command")
                return
            await set_cookie(args.task_type, args.cookie_string, args.refresh_token)
        elif args.command == 'enable':
            if not args.task_type:
                print("Error: task_type is required for 'enable' command")
                return
            await enable_task(args.task_type)
        elif args.command == 'disable':
            if not args.task_type:
                print("Error: task_type is required for 'disable' command")
                return
            await disable_task(args.task_type)
        elif args.command == 'refresh':
            if not args.task_type:
                print("Error: task_type is required for 'refresh' command")
                return
            await refresh_cookie(args.task_type)
        elif args.command == 'refresh-all':
            await refresh_all()
        elif args.command == 'test':
            if not args.task_type:
                print("Error: task_type is required for 'test' command")
                return
            await test_cookie(args.task_type)
        else:
            print(f"Unknown command: {args.command}")
            print_help()
    
    except Exception as e:
        logger.error(f"Error executing command '{args.command}': {e}")
        print(f"Error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
