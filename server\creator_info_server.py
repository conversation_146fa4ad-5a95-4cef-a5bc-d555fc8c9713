import asyncio
import json
import aiohttp
from bilibili_api import Credential
from datetime import datetime
import asyncpg
import re
import time
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from logger import logger
from sql import creator_sql
from backend.utils.db_pool import get_connection
from const import (
    CREATER_BUVID4,
    CREATER_SESSDATA,
    CREATER_BILI_JCT,
    CREATER_BUVID3,
    CREATER_DEDEUSERID,
)

# Import cookie management
try:
    from .cookie_manager import get_task_cookie
    COOKIE_MANAGER_AVAILABLE = True
except ImportError:
    try:
        from cookie_manager import get_task_cookie
        COOKIE_MANAGER_AVAILABLE = True
    except ImportError:
        COOKIE_MANAGER_AVAILABLE = False
try:
    from creator_scheduler_config import (
        get_enabled_functions,
        get_function_params,
        get_scheduler_config,
        SPECIAL_FUNCTIONS_CONFIG
    )
except ImportError:
    def get_enabled_functions():
        return {
            'overview': ['fetch_overview_stat', 'fetch_attention_analyze', 'fetch_archive_analyze', 'fetch_video_overview'],
            'fans': ['fetch_fan_graph', 'fetch_fan_overview'],
            'video': ['fetch_video_compare', 'fetch_video_pandect', 'fetch_video_survey', 'fetch_video_source', 'fetch_video_view_data']
        }
    def get_function_params(func_name):
        defaults = {
            'fetch_archive_analyze': {'period': 0},
            'fetch_video_compare': {'size': 1000},
            'fetch_video_survey': {'data_type': 1}
        }
        return defaults.get(func_name, {})
    def get_scheduler_config():
        return {'function_delay': 5, 'category_delay': 10, 'max_retries': 3}


SQL_FAIL_DELAY = 3
HTTP_FAIL_DELAY = 5
MAX_HTTP_RETRIES = 3


class CreatorInfoServer:
    def __init__(self, uid="401315430", credential=None, db_conn=None):
        if credential:
            self.creator_credential = credential
        else:
            # Try to get creator task cookie first
            creator_credential = self._get_creator_task_credential()
            if creator_credential:
                self.creator_credential = creator_credential
            else:
                # Fallback to const.py values
                self.creator_credential = Credential(
                    sessdata=CREATER_SESSDATA,
                    bili_jct=CREATER_BILI_JCT,
                    buvid3=CREATER_BUVID3,
                    dedeuserid=CREATER_DEDEUSERID,
                    buvid4=CREATER_BUVID4,
                )
    
        self.conn = db_conn
        self.uid = uid
        self.db_pool = get_connection

        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }

    def _get_creator_task_credential(self):
        """Get credential from creator task cookie"""
        if not COOKIE_MANAGER_AVAILABLE:
            return None

        try:
            cookie_str = get_task_cookie('creator')
            if not cookie_str:
                return None

            # Parse cookie string to extract individual values
            cookie_dict = {}
            for item in cookie_str.split('; '):
                if '=' in item:
                    key, value = item.split('=', 1)
                    cookie_dict[key] = value

            # Create Credential object
            credential = Credential(
                sessdata=cookie_dict.get('SESSDATA', ''),
                bili_jct=cookie_dict.get('bili_jct', ''),
                buvid3=cookie_dict.get('buvid3', ''),
                dedeuserid=cookie_dict.get('DedeUserID', ''),
                buvid4=cookie_dict.get('buvid4', '')
            )

            return credential

        except Exception as e:
            logger.error(f"Error getting creator task credential: {e}")
            return None

    async def initialize_async(self):
        await self._create_tables()

    async def _create_tables(self):
        """
        Create database tables.
        """
        logger.info("Creating database tables if not exists...")
        await self._execute_sql(creator_sql.create_video_compare_table_sql)
        await self._execute_sql(creator_sql.create_video_pandect_table_sql)
        await self._execute_sql(creator_sql.create_video_view_data_table_sql)

        await self._execute_sql(creator_sql.create_overview_stat_table_sql)
        await self._execute_sql(creator_sql.create_attention_analyze_table_sql)
        await self._execute_sql(creator_sql.create_archive_analyze_table_sql)
        await self._execute_sql(creator_sql.create_video_overview_table_sql)
        await self._execute_sql(creator_sql.create_fan_graph_table_sql)
        await self._execute_sql(creator_sql.create_fan_overview_table_sql)
        await self._execute_sql(creator_sql.create_video_survey_table_sql)
        await self._execute_sql(creator_sql.create_video_source_table_sql)

        logger.info("Database table creation complete.")

    async def _execute_sql(self, sql, params=None, fetch_one=False, fetch_all=False, executemany_params=None, max_retries=3):
        """
        Helper function to execute SQL with smart retry logic, using a connection pool (asynchronous version).
        
        Args:
            sql: The SQL statement to execute.
            params: Parameters for a single SQL execution.
            fetch_one: Whether to fetch a single row result.
            fetch_all: Whether to fetch all results.
            executemany_params: A list of parameter tuples for executemany.
            max_retries: Maximum number of retries.
            
        Returns:
            Query results or None (if execution fails).
        """

        retriable_errors = (
            asyncpg.exceptions.PostgresConnectionError,
            asyncpg.exceptions.InterfaceError,
            asyncpg.exceptions.ConnectionDoesNotExistError,
        )
        
        retry_count = 0
        last_exception = None # To store the exception if all retries fail

        while retry_count <= max_retries:
            try:
                async with self.db_pool() as db_conn:
                    if executemany_params:
                        await db_conn.executemany(sql, executemany_params)
                        return None # Explicitly return None for executemany, consistent with DML
                    else:
                        if fetch_one:
                            result = await db_conn.fetchrow(sql, *params if params else [])
                            return result # asyncpg.Record or None
                        elif fetch_all:
                            result = await db_conn.fetch(sql, *params if params else [])
                            return result # list of asyncpg.Record
                        else:
                            # For INSERT/UPDATE/DELETE or DDL
                            await db_conn.execute(sql, *params if params else [])
                            return None

            except retriable_errors as e:
                last_exception = e
                retry_count += 1
                if retry_count <= max_retries:
                    delay = SQL_FAIL_DELAY * (2 ** (retry_count - 1))
                    logger.warning(
                        f"Encountered retriable error during SQL execution (Attempt {retry_count}/{max_retries}): {e}\n"
                        f"SQL: {sql}\n"
                        f"Retrying in {delay} seconds"
                    )
                    await asyncio.sleep(delay)
                else:
                    logger.error(
                        f"Max retries ({max_retries}) reached for SQL execution, giving up: {e}\n"
                        f"SQL: {sql}"
                    )
                    return None # Propagate failure after retries
            
            except asyncpg.exceptions.DuplicateTableError as e:
                # Check if this is a CREATE TABLE IF NOT EXISTS statement
                if re.match(r"^\s*CREATE\s+TABLE\s+IF\s+NOT\s+EXISTS", sql, re.IGNORECASE):
                    logger.warning(
                        f"Table already exists, which is normally expected and handled by 'CREATE TABLE IF NOT EXISTS'. "
                        f"This warning indicates the command still raised DuplicateTableError. SQL: {sql}. Error: {e}"
                    )
                    return None 
                else:
                    logger.error(
                        f"Unhandled DuplicateTableError error during SQL execution: {e}\n"
                        f"SQL: {sql}"
                    )
                    last_exception = e # Store exception before returning None or re-raising
                    return None # Or consider re-raising if this should be fatal
            
            except asyncpg.exceptions.PostgresError as e: # Catch other PostgreSQL specific errors
                logger.error(
                    f"Non-retriable error during SQL execution: {e}\n"
                    f"SQL: {sql}"
                )
                last_exception = e
                return None

        # If loop finishes due to max_retries, last_exception will be set
        if last_exception:
            logger.error(f"SQL execution failed after {max_retries} retries with last error: {last_exception}")
        # If loop finishes due to max_retries, last_exception will be set
        if last_exception:
            logger.error(f"SQL execution failed after {max_retries} retries with last error: {last_exception}")
        return None

    async def _fetch_with_retry(self, url, params=None, max_retries=MAX_HTTP_RETRIES):
        """
        Helper function to execute HTTP GET requests with smart retry logic (asynchronous version).

        Args:
            url: The URL to fetch.
            params: Dictionary of query parameters.
            max_retries: Maximum number of retries.

        Returns:
            JSON response data or None (if fetching fails).
        """
        retry_count = 0
        last_exception = None

        while retry_count <= max_retries:
            try:
                async with aiohttp.ClientSession(headers=self.headers) as session:
                    cookies = self.creator_credential.get_cookies()

                    async with session.get(url, params=params, cookies=cookies) as response:
                        response.raise_for_status()  # Raises an exception for bad status codes (4xx or 5xx)
                        return await response.json()
            except aiohttp.ClientError as e:
                last_exception = e
                retry_count += 1
                if retry_count <= max_retries:
                    delay = HTTP_FAIL_DELAY * (2 ** (retry_count - 1))
                    logger.warning(
                        f"Encountered retriable HTTP error during fetch (Attempt {retry_count}/{max_retries}): {e}\n"
                        f"URL: {url}\n"
                        f"Retrying in {delay} seconds"
                    )
                    await asyncio.sleep(delay)
                else:
                    logger.error(
                        f"Max retries ({max_retries}) reached for HTTP fetch, giving up: {e}\n"
                        f"URL: {url}"
                    )
                    return None
            except Exception as e: # Catch any other unexpected errors
                logger.error(f"An unexpected error occurred during HTTP fetch: {e}\nURL: {url}")
                return None

        if last_exception:
            logger.error(f"HTTP fetch failed after {max_retries} retries with last error: {last_exception}")
        return None

    async def _insert_data(self, sql, data, table_name):
        """
        通用数据插入方法，处理单个或批量插入。
        Generic data insertion method, handling single or bulk insertions.
        """
        if not data:
            logger.info(f"No data to insert into {table_name}.")
            return

        if isinstance(data, list) and all(isinstance(d, tuple) for d in data):
            # Bulk insertion
            logger.info(f"Attempting to insert {len(data)} records into {table_name}...")
            await self._execute_sql(sql, executemany_params=data)
            logger.info(f"Finished inserting {len(data)} records into {table_name}.")
        elif isinstance(data, tuple):
            # Single insertion
            logger.info(f"Attempting to insert 1 record into {table_name}...")
            await self._execute_sql(sql, params=data)
            logger.info(f"Finished inserting 1 record into {table_name}.")
        else:
            logger.error(f"Invalid data format for insertion into {table_name}. Expected tuple or list of tuples.")

#   ----------------------------------  Overview  --------------------------------------

    async def fetch_overview_stat(self):
        """
        url: https://member.bilibili.com/x/web/index/stat
       
        args: cookie

        returns: data.
        inc_coin	num	新增投币数	
        inc_elec	num	新增充电数	
        inc_fav	num	新增收藏数	
        inc_like	num	新增点赞数	
        inc_share	num	新增分享数	
        incr_click	num	新增播放数	
        incr_dm	num	新增弹幕数	
        incr_fans	num	新增粉丝数	
        incr_reply	num	新增评论数	
        total_click	num	总计播放数	
        total_coin	num	总计投币数	
        total_dm	num	总计弹幕数	
        total_elec	num	总计充电数	
        total_fans	num	总计粉丝数	
        total_fav	num	总计收藏数	
        total_like	num	总计点赞数	
        total_reply	num	总计评论数	
        total_share	num	总计分享数
        
        """
        url = "https://member.bilibili.com/x/web/index/stat"
        result = await self._fetch_with_retry(url)

        if result and result.get('code') == 0 and result.get('data'):
            
            current_timestamp = int(time.time())
            current_datetime = datetime.fromtimestamp(current_timestamp).replace(hour=0, minute=0, second=0, microsecond=0)

            data = result['data']

            # 存储概览统计数据
            record = (
                self.uid,
                data.get('inc_coin'),
                data.get('inc_elec'),
                data.get('inc_fav'),
                data.get('inc_like'),
                data.get('inc_share'),
                data.get('incr_click'),
                data.get('incr_dm'),
                data.get('incr_fans'),
                data.get('incr_reply'),
                data.get('total_click'),
                data.get('total_coin'),
                data.get('total_dm'),
                data.get('total_elec'),
                data.get('total_fans'),
                data.get('total_fav'),
                data.get('total_like'),
                data.get('total_reply'),
                data.get('total_share'),
                current_timestamp,
                current_datetime
            )

            await self._insert_data(creator_sql.insert_overview_stat_table_sql, [record], "overview_stat_table")

        return result

    async def fetch_attention_analyze(self):
        """
        涨粉分析 - 获取账号诊断中的涨粉分析数据

        API详情:
        - URL: https://member.bilibili.com/x/web/data/v2/account_diagnose/attention_analyze
        - 方法: GET
        - 认证: 需要cookie认证

        返回数据结构:
        - 涨粉趋势数据
        - 粉丝增长分析
        - 涨粉来源分析

        Returns:
            dict: API响应数据，包含涨粉分析信息
        """
        url = "https://member.bilibili.com/x/web/data/v2/account_diagnose/attention_analyze"
        result = await self._fetch_with_retry(url)

        if result and result.get('code') == 0 and result.get('data'):
            
            current_timestamp = int(time.time())
            current_datetime = datetime.fromtimestamp(current_timestamp).replace(hour=0, minute=0, second=0, microsecond=0)

            
            record = (
                self.uid,
                json.dumps(result['data']),
                current_timestamp,
                current_datetime
            )

            await self._insert_data(creator_sql.insert_attention_analyze_table_sql, [record], "attention_analyze_table")

        return result

    async def fetch_archive_analyze(self, period: int = 0):
        """
        播放分析 - 获取账号诊断中的播放分析数据

        API详情:
        - URL: https://member.bilibili.com/x/web/data/v2/account_diagnose/play_analyze
        - 方法: GET
        - 认证: 需要cookie认证

        Args:
            period (int): 分析周期，默认为0

        返回数据结构:
        - 播放量趋势数据
        - 播放来源分析
        - 播放设备分析
        - 播放时长分析

        Returns:
            dict: API响应数据，包含播放分析信息
        """
        url = "https://member.bilibili.com/x/web/data/v2/account_diagnose/play_analyze"
        params = {"period": period}
        result = await self._fetch_with_retry(url, params=params)

        if result and result.get('code') == 0 and result.get('data'):
            current_timestamp = int(time.time())
            current_datetime = datetime.fromtimestamp(current_timestamp).replace(hour=0, minute=0, second=0, microsecond=0)

            record = (
                self.uid,
                period,
                json.dumps(result['data']),
                current_timestamp,
                current_datetime
            )

            await self._insert_data(creator_sql.insert_archive_analyze_table_sql, [record], "archive_analyze_table")

        return result


    async def fetch_video_overview(self):
        """
        https://member.bilibili.com/x/web/data/v2/account_diagnose/overview

        return eg:
        {
            "code": 0,
            "message": "0",
            "ttl": 1,
            "data": {
                "play_cnt": {
                    "amount": 316135,
                    "amount_pass_per": 6206,
                    "amount_last": 576728,
                    "amount_last_pass_per": 7374,
                    "amount_change": -260593,
                    "amount_med": 0,
                    "date": ********,
                    "tendency_list": null
                },
                "interact_rate": {
                    "amount": 703,
                    "amount_pass_per": 6063,
                    "amount_last": 1075,
                    "amount_last_pass_per": 8337,
                    "amount_change": -3460,
                    "amount_med": 0,
                    "date": ********,
                    "tendency_list": null
                }
            }
        }
        """
        url = "https://member.bilibili.com/x/web/data/v2/account_diagnose/overview"
        result = await self._fetch_with_retry(url)

        if result and result.get('code') == 0 and result.get('data'):
            current_timestamp = int(time.time())
            current_datetime = datetime.fromtimestamp(current_timestamp).replace(hour=0, minute=0, second=0, microsecond=0)

            data = result['data']
            records_to_insert = []

            for data_type, details in data.items():
                record = (
                    self.uid,
                    data_type,
                    details.get('amount'),
                    details.get('amount_pass_per'),
                    details.get('amount_last'),
                    details.get('amount_last_pass_per'),
                    details.get('amount_change'),
                    details.get('amount_med'),
                    details.get('date'),
                    json.dumps(details.get('tendency_list')),
                    current_timestamp,
                    current_datetime
                )
                records_to_insert.append(record)

            await self._insert_data(creator_sql.insert_video_overview_table_sql, records_to_insert, "video_overview_table")

        return result


#   ----------------------------------  Overview  --------------------------------------


#   ------------------------------------  Fans -----------------------------------------

    async def fetch_fan_graph(self):
        """
        粉丝数据图表 - 获取粉丝增长改进建议和图表数据

        API详情:
        - URL: https://member.bilibili.com/x/web/data/account_diagnose/attention_improve_idea
        - 方法: GET
        - 认证: 需要cookie认证

        返回数据结构:
        - 粉丝增长趋势图表数据
        - 粉丝活跃度分析
        - 粉丝增长改进建议
        - 粉丝来源分析

        Returns:
            dict: API响应数据，包含粉丝图表和改进建议
        """
        url = "https://member.bilibili.com/x/web/data/account_diagnose/attention_improve_idea"
        result = await self._fetch_with_retry(url)

        if result and result.get('code') == 0 and result.get('data'):
            
            current_timestamp = int(time.time())
            current_datetime = datetime.fromtimestamp(current_timestamp).replace(hour=0, minute=0, second=0, microsecond=0)

            
            record = (
                self.uid,
                json.dumps(result['data']),
                current_timestamp,
                current_datetime
            )

            await self._insert_data(creator_sql.insert_fan_graph_table_sql, [record], "fan_graph_table")

        return result

    async def fetch_fan_overview(self):
        """
        涨粉分析
        url: https://member.bilibili.com/x/web/data/account_diagnose/active_fans_improve_idea
        args: cookie
        returns: data.

        EG:
        {
            "code": 0,
            "message": "0",
            "ttl": 1,
            "data": {
                "recent_create_tags": "虚拟主播,虚拟偶像,舞台,音乐现场,性感,现场,搞笑,反转",
                "fans_tags": "虚拟偶像,虚拟主播,Cosplay,cos,LPL,三角洲,可爱,射击游戏",
                "recent_create_tags_value": "1700165,1605246,635256,398924,354300,341697,324962,279217",
                "fans_tags_value": "860.00,688.89,300.00,252.94,200.00,190.91,179.82,179.31",
                "active_fans_pass_per": 7917,
                "fans_active_period": {
                    "day_of_week": 6,
                    "hour": 22
                },
                "recent_archive_active_period": [
                    {
                        "day_of_week": 1,
                        "hour": 19
                    },
                    {
                        "day_of_week": 6,
                        "hour": 18
                    },
                    {
                        "day_of_week": 6,
                        "hour": 6
                    },
                    {
                        "day_of_week": 5,
                        "hour": 16
                    },
                    {
                        "day_of_week": 4,
                        "hour": 4
                    }
                ],
                "fans_active_week_day": [
                    {
                        "period": 1,
                        "cnt": 1396
                    },
                    {
                        "period": 2,
                        "cnt": 1340
                    },
                    {
                        "period": 3,
                        "cnt": 1342
                    },
                    {
                        "period": 4,
                        "cnt": 1357
                    },
                    {
                        "period": 5,
                        "cnt": 1432
                    },
                    {
                        "period": 6,
                        "cnt": 1572
                    },
                    {
                        "period": 7,
                        "cnt": 1557
                    }
                ],
                "fans_active_hour": [
                    {
                        "period": 0,
                        "cnt": 482
                    },
                    {
                        "period": 1,
                        "cnt": 365
                    },
                    {
                        "period": 2,
                        "cnt": 255
                    },
                    {
                        "period": 3,
                        "cnt": 175
                    },
                    {
                        "period": 4,
                        "cnt": 127
                    },
                    {
                        "period": 5,
                        "cnt": 116
                    },
                    {
                        "period": 6,
                        "cnt": 170
                    },
                    {
                        "period": 7,
                        "cnt": 268
                    },
                    {
                        "period": 8,
                        "cnt": 331
                    },
                    {
                        "period": 9,
                        "cnt": 373
                    },
                    {
                        "period": 10,
                        "cnt": 411
                    },
                    {
                        "period": 11,
                        "cnt": 477
                    },
                    {
                        "period": 12,
                        "cnt": 550
                    },
                    {
                        "period": 13,
                        "cnt": 513
                    },
                    {
                        "period": 14,
                        "cnt": 468
                    },
                    {
                        "period": 15,
                        "cnt": 459
                    },
                    {
                        "period": 16,
                        "cnt": 475
                    },
                    {
                        "period": 17,
                        "cnt": 524
                    },
                    {
                        "period": 18,
                        "cnt": 568
                    },
                    {
                        "period": 19,
                        "cnt": 575
                    },
                    {
                        "period": 20,
                        "cnt": 578
                    },
                    {
                        "period": 21,
                        "cnt": 584
                    },
                    {
                        "period": 22,
                        "cnt": 585
                    },
                    {
                        "period": 23,
                        "cnt": 558
                    }
                ]
            }
        }
        """
        url = "https://member.bilibili.com/x/web/data/account_diagnose/active_fans_improve_idea"
        result = await self._fetch_with_retry(url)

        if result and result.get('code') == 0 and result.get('data'):
            
            current_timestamp = int(time.time())
            current_datetime = datetime.fromtimestamp(current_timestamp).replace(hour=0, minute=0, second=0, microsecond=0)

            data = result['data']

            # 存储粉丝概览数据
            record = (
                self.uid,
                data.get('recent_create_tags'),
                data.get('fans_tags'),
                data.get('recent_create_tags_value'),
                data.get('fans_tags_value'),
                data.get('active_fans_pass_per'),
                json.dumps(data.get('fans_active_period', {})),
                json.dumps(data.get('recent_archive_active_period', [])),
                json.dumps(data.get('fans_active_week_day', [])),
                json.dumps(data.get('fans_active_hour', [])),
                current_timestamp,
                current_datetime
            )

            await self._insert_data(creator_sql.insert_fan_overview_table_sql, [record], "fan_overview_table")

        return result


#   ------------------------------------  Fans -----------------------------------------


#   ----------------------------------  Video -----------------------------------------

    async def fetch_video_compare(self, size: int = 1000): # 将size设置为1000
        """
        UP主视频数据比较
        url: https://member.bilibili.com/x/web/data/archive_diagnose/compare
        cookie

        returns:
            字段	类型	内容	备注
            aid	num	av号	
            bvid	str	bv号	
            cover	str	封面url	
            title	str	标题	
            pubtime	num	发布时间	
            duration	num	视频长度（秒）	
            play	num	播放数	
            vt	num	未知	
            like	num	点赞数	
            comment	num	评论数	
            dm	num	弹幕数	
            fav	num	收藏数	
            coin	num	投币数	
            share	num	分享数	
            full_play_ratio	num	完播比，用户平均在百分之多少退出	
            play_viewer_rate	num	游客播放数，这个视频有多少是游客播放	
            active_fans_rate	num	粉丝观看率，多少粉丝看了这个视频	
            active_fans_med	num	?	
            tm_rate	num	封标点击率	
            tm_rate_med	num	你自己平均封标点击率	
            tm_fan_simi_rate_med	num	同类up粉丝封标点击率	
            tm_viewer_simi_rate_med	num	同类up游客封标点击率	
            tm_fan_rate	num	粉丝封标点击率	
            tm_viewer_rate	num	游客封标点击率	
            tm_pass_rate	num	封标点击率超过n%同类稿件	
            tm_fan_pass_rate	num	粉丝封标点击率超过n%同类稿件	
            tm_viewer_pass_rate	num	游客封标点击率超过n%同类稿件	
            crash_rate	num	3秒退出率	
            crash_rate_med	num	?	
            crash_fan_simi_rate_med	num	同类up粉丝3秒退出率	
            crash_viewer_simi_rate_med	num	同类up游客3秒退出率	
            crash_fan_rate	num	粉丝3秒退出率	
            crash_viewer_rate	num	游客3秒退出率	
            interact_rate	num	互动率	
            interact_rate_med	num		
            interact_fan_simi_rate_med	num	同类up粉丝互动率	
            interact_viewer_simi_rate_med	num	同类up游客互动率	
            interact_fan_rate	num	粉丝互动率	
            interact_viewer_rate	num	游客互动率	
            avg_play_time	num	平均播放时间
            total_new_attention_cnt	num	涨粉	
            play_trans_fan_rate	num	播转粉率	
            play_trans_fan_rate_med	num	其他up平均播转粉率
        """
        url = "https://member.bilibili.com/x/web/data/archive_diagnose/compare"
        params = {
            'size': size
        }
        result = await self._fetch_with_retry(url, params=params)
        
        if result and result.get('code') == 0 and result.get('data') and result['data'].get('list'):
            records_to_insert = []
            for item in result['data']['list']:
                
                current_timestamp = int(time.time())
                current_datetime = datetime.fromtimestamp(current_timestamp).replace(hour=0, minute=0, second=0, microsecond=0)

                record = (
                    self.uid,
                    item.get('aid'),
                    item.get('bvid'),
                    item.get('cover'),
                    item.get('title'),
                    item.get('duration'),
                    json.dumps(item.get('stat')),
                    item.get('is_only_self'),
                    json.dumps(item.get('hour_stat')),
                    item.get('pubtime'),
                    current_timestamp,
                    current_datetime
                )
                records_to_insert.append(record)
            
            await self._insert_data(creator_sql.insert_video_compare_table_sql, records_to_insert, "video_compare_table")
        return result

    async def fetch_video_pandect(self):
        """
        视频数据增量趋势
        url: https://member.bilibili.com/x/web/data/pandect
        args: cookies 前30天
            data_type: int
            1	播放
            2	弹幕
            3	评论
            4	分享
            5	投币
            6	收藏
            7	充电
            8	点赞

        returns: 

            data数组：
            项	类型	内容	备注
            0	obj	1天前的数据	
            n	obj	（n+1）天前的数据	
            ……	obj	……	……
            29	obj	30天前的数据	最后一条
            data数组中的对象：

            字段	类型	内容	备注
            date_key	num	对应时间	时间戳 前一天的8:00
            total_inc	num	增加数量	意义为数据类型决定

        """
        url = "https://member.bilibili.com/x/web/data/pandect"

        data_type_map = {
            1: 'play_total_inc',
            2: 'dm_total_inc',
            3: 'comment_total_inc',
            4: 'share_total_inc',
            5: 'coin_total_inc',
            6: 'fav_total_inc',
            7: 'elec_total_inc',
            8: 'like_total_inc'
        }

        for data_type, column_name in data_type_map.items():
            params = {"type": data_type}
            result = await self._fetch_with_retry(url, params=params)

            if result and result.get('code') == 0 and result.get('data'):
                records_to_insert = []
                for item in result['data']:
                    
                    current_timestamp = int(time.time())
                    current_datetime = datetime.fromtimestamp(current_timestamp).replace(hour=0, minute=0, second=0, microsecond=0)

                    record = (
                        self.uid,
                        item.get('date_key'),
                        item.get('total_inc'),
                        column_name,
                        current_timestamp,
                        current_datetime
                    )
                    records_to_insert.append(record)
                
                insert_sql = f"""
                    INSERT INTO video_pandect_table (uid, date_key, total_inc, data_type_column, create_time, update_time)
                    VALUES ($1, $2, $3, $4, $5, $6)
                    ON CONFLICT (uid, date_key, data_type_column) DO UPDATE SET
                        total_inc = EXCLUDED.total_inc,
                        update_time = EXCLUDED.update_time;
                """
                await self._insert_data(insert_sql, records_to_insert, "video_pandect_table")
                await asyncio.sleep(3)
        return result

    async def fetch_video_survey(self, data_type: int = 1):
        """
        稿件操作来源占比情况
        url: https://member.bilibili.com/x/web/data/survey
        args: cookies 前30天
            data_type: int
            1	播放
            2	弹幕
            3	评论
            4	分享
            5	投币
            6	收藏
            7	充电
            8	点赞

        returns: 

            data对象：

            字段	类型	内容	备注
            ｛YYYYMMDD｝	obj	上一天的情况	字段名为日期
            data中的｛YYYYMMDD｝对象：

            字段	类型	内容	备注
            arc_inc	array	稿件情况	
            total_inc	num	总计增长情况	
            type_rank	obj	分区排名情况	
            ｛YYYYMMDD｝中的arc_inc数组：

            项	类型	内容	备注
            0	obj	稿件1	
            n	obj	稿件（n+1）	
            ……	obj	……	……
            ｛YYYYMMDD｝中的arc_inc数组中的对象：

            字段	类型	内容	备注
            aid	num	稿件avid	
            bvid	str	稿件bvid	
            daytime	num	统计时间	时间戳
            incr	num	稿件增长情况数	
            interactive	num	0	作用尚不明确
            ptime	num	稿件发布时间	时间戳
            title	str	稿件标题	
            
            ｛YYYYMMDD｝中的type_rank对象：

            字段	类型	内容	备注
            {分区名}	num	该排名数	
            ……	num	……	……

        """
        url = "https://member.bilibili.com/x/web/data/survey"
        params = {"type": data_type} # 使用传入的data_type参数
        result = await self._fetch_with_retry(url, params=params)

        if result and result.get('code') == 0 and result.get('data'):
            
            current_timestamp = int(time.time())
            current_datetime = datetime.fromtimestamp(current_timestamp).replace(hour=0, minute=0, second=0, microsecond=0)

            record = (
                self.uid,
                data_type,
                json.dumps(result['data']),
                current_timestamp,
                current_datetime
            )

            await self._insert_data(creator_sql.insert_video_survey_table_sql, [record], "video_survey_table")

        return result

    async def fetch_video_source(self):
        """
        稿件操作来源占比情况
        url: https://member.bilibili.com/x/web/data/playsource
        args: cookies 

        returns: 

            data对象：

            字段	类型	内容	备注
            page_source	obj	播放方式情况	
            play_proportion	obj	播放平台情况	
            data中的page_source对象：

            字段	类型	内容	备注
            dynamic	num	通过动态	
            other	num	其他方式	
            related_video	num	通过推荐列表	
            search	num	通过搜索	
            space	num	空间列表播放	
            tenma	num	天马（APP推荐信息流）来源	
            data中的play_proportion对象：

            字段	类型	内容	备注
            android	num	安卓端	
            h5	num	移动h5端页面	
            ios	num	ios端	
            out	num	站外	
            pc	num	电脑版网页	

        """
        url = "https://member.bilibili.com/x/web/data/playsource"
        result = await self._fetch_with_retry(url)

        if result and result.get('code') == 0 and result.get('data'):
            
            current_timestamp = int(time.time())
            current_datetime = datetime.fromtimestamp(current_timestamp).replace(hour=0, minute=0, second=0, microsecond=0)

            data = result['data']

            # 存储视频来源数据
            record = (
                self.uid,
                json.dumps(data.get('page_source', {})),
                json.dumps(data.get('play_proportion', {})),
                current_timestamp,
                current_datetime
            )

            await self._insert_data(creator_sql.insert_video_source_table_sql, [record], "video_source_table")

        return result

    async def fetch_video_view_data(self):
        """
        播放分布情况（粉丝与路人）
        url: https://member.bilibili.com/x/web/data/base
        args: cookies 

        returns: 

            data对象：

            字段	类型	内容	备注
            period	obj	提示信息	
            viewer_area	obj	播放地区情况	
            viewer_base	obj	播放数据情况	
            data中的period对象：

            字段	类型	内容	备注
            module_one	str	???	
            module_two	str	???	
            module_three	str	???	
            module_four	str	???	
            data中的viewer_area对象：

            字段	类型	内容	备注
            fan	obj	粉丝播放地区情况	
            not_fan	obj	路人播放地区情况	
            viewer_area中的fan对象：

            字段	类型	内容	备注
            {行政区名}	num	该地区的粉丝播放量	字段名为行政区名
            ……	num	……	……
            viewer_area中的not_fan对象：

            字段	类型	内容	备注
            {行政区名}	num	该地区的路人播放量	字段名为行政区名
            ……	num	……	……
            data中的viewer_base对象：

            字段	类型	内容	备注
            fan	obj	粉丝播放数据情况	
            not_fan	obj	路人播放数据情况	
            viewer_base中的fan对象：

            字段	类型	内容	备注
            male	num	男性粉丝播放数	
            female	num	女性粉丝播放数	
            age_one	num	0-16岁粉丝播放数	
            age_two	num	16-25岁粉丝播放数	
            age_three	num	25-40岁粉丝播放数	
            age_four	num	40+岁粉丝播放数	
            plat_pc	num	pc网页端粉丝播放数	
            plat_h5	num	移动h5端粉丝播放数	
            plat_out	num	站外端粉丝播放数	
            plat_ios	num	ios端粉丝播放数	
            plat_android	num	安卓端粉丝播放数	
            plat_other_app	num	其他粉丝播放数	
            viewer_base中的not_fan对象：

            字段	类型	内容	备注
            male	num	男性路人播放数	
            female	num	女性路人播放数	
            age_one	num	0-16岁路人播放数	
            age_two	num	16-25岁路人播放数	
            age_three	num	25-40岁路人播放数	
            age_four	num	40+岁路人播放数	
            plat_pc	num	pc网页端路人播放数	
            plat_h5	num	移动h5端路人播放数	
            plat_out	num	站外端路人播放数	
            plat_ios	num	ios端路人播放数	
            plat_android	num	安卓端路人播放数	
            plat_other_app	num	其他路人播放数	

        """       
        url = "https://member.bilibili.com/x/web/data/base"
        result = await self._fetch_with_retry(url)

        if result and result.get('code') == 0 and result.get('data'):
            data = result['data']
            current_timestamp = int(time.time())
            current_datetime = datetime.fromtimestamp(current_timestamp).replace(hour=0, minute=0, second=0, microsecond=0)



            # 处理 viewer_area 数据
            viewer_area_fan = data.get('viewer_area', {}).get('fan', {})
            viewer_area_not_fan = data.get('viewer_area', {}).get('not_fan', {})
            
            # 处理 viewer_base 数据
            viewer_base_fan = data.get('viewer_base', {}).get('fan', {})
            viewer_base_not_fan = data.get('viewer_base', {}).get('not_fan', {})

            record = (
                self.uid,
                json.dumps(viewer_area_fan), # jsonb
                json.dumps(viewer_area_not_fan), # jsonb
                json.dumps(viewer_base_fan), # jsonb
                json.dumps(viewer_base_not_fan), # jsonb
                current_timestamp,
                current_datetime
            )
            await self._insert_data(creator_sql.insert_video_view_data_table_sql, record, "video_view_data_table")
        return result
#   ----------------------------------  Video -----------------------------------------


#   ----------------------------------  Scheduler -----------------------------------------

class CreatorInfoScheduler:
    """
    Creator Info Server 定时调度系统
    Scheduled task system for Creator Info Server
    """

    def __init__(self, uid="401315430", credential=None):
        self.uid = uid
        self.credential = credential
        self.creator_server = None
        self.scheduler = None
        self.is_running = False

        # 从配置文件获取业务函数分组
        self.business_functions = get_enabled_functions()
        self.config = get_scheduler_config()

    async def initialize(self):
        """初始化 Creator Server 实例"""
        self.creator_server = CreatorInfoServer(uid=self.uid, credential=self.credential)
        await self.creator_server.initialize_async()
        logger.info(f"CreatorInfoScheduler initialized for UID: {self.uid}")

    async def execute_function_safely(self, func_name, category):
        """
        安全执行单个业务函数
        Safely execute a single business function
        """
        try:
            if not self.creator_server:
                logger.error(f"Creator server not initialized for function: {func_name}")
                return False

            func = getattr(self.creator_server, func_name, None)
            if not func:
                logger.error(f"Function {func_name} not found in CreatorInfoServer")
                return False

            logger.info(f"Starting execution of {func_name} in category {category}")
            start_time = time.time()

            # 获取函数参数
            params = get_function_params(func_name)

            # 执行函数
            if params:
                result = await func(**params)
                logger.info(f"Executed {func_name} with params: {params}")
            else:
                result = await func()

            execution_time = time.time() - start_time
            logger.info(f"Successfully executed {func_name} in {execution_time:.2f}s")

            return True

        except Exception as e:
            logger.error(f"Error executing {func_name} in category {category}: {e}")
            return False

    async def execute_category_functions(self, category):
        """
        执行指定分类的所有函数
        Execute all functions in a specific category
        """
        if category not in self.business_functions:
            logger.error(f"Unknown category: {category}")
            return

        functions = self.business_functions[category]
        logger.info(f"Starting execution of {category} category with {len(functions)} functions")

        success_count = 0
        total_count = len(functions)

        for func_name in functions:
            success = await self.execute_function_safely(func_name, category)
            if success:
                success_count += 1

            # 函数间延迟，避免请求过于频繁
            await asyncio.sleep(self.config.get('function_delay', 5))

        logger.info(f"Category {category} execution completed: {success_count}/{total_count} functions succeeded")

    async def execute_all_functions(self):
        """
        执行所有业务函数
        Execute all business functions
        """
        logger.info("Starting daily execution of all Creator Info functions")
        start_time = time.time()

        total_success = 0
        total_functions = sum(len(funcs) for funcs in self.business_functions.values())

        for category in self.business_functions.keys():
            await self.execute_category_functions(category)
            # 分类间延迟
            await asyncio.sleep(self.config.get('category_delay', 10))

        execution_time = time.time() - start_time
        logger.info(f"Daily execution completed in {execution_time:.2f}s")

    def start_scheduler(self, daily_hour=2, daily_minute=0):
        """
        启动调度器
        Start the scheduler

        Args:
            daily_hour: 每日执行的小时 (0-23)
            daily_minute: 每日执行的分钟 (0-59)
        """
        if self.is_running:
            logger.warning("Scheduler is already running")
            return

        self.scheduler = AsyncIOScheduler()

        # 添加每日任务
        self.scheduler.add_job(
            self.execute_all_functions,
            'cron',
            hour=daily_hour,
            minute=daily_minute,
            id='daily_creator_info_task',
            name='Daily Creator Info Data Collection'
        )

        # 立即执行一次（可选）
        self.scheduler.add_job(
            self.execute_all_functions,
            'date',
            run_date=datetime.now(),
            id='immediate_creator_info_task',
            name='Immediate Creator Info Data Collection'
        )

        self.scheduler.start()
        self.is_running = True

        logger.info(f"CreatorInfoScheduler started - Daily execution at {daily_hour:02d}:{daily_minute:02d}")

    def stop_scheduler(self):
        """停止调度器"""
        if self.scheduler and self.is_running:
            self.scheduler.shutdown()
            self.is_running = False
            logger.info("CreatorInfoScheduler stopped")
        else:
            logger.warning("Scheduler is not running")

    def get_scheduler_status(self):
        """获取调度器状态"""
        if not self.scheduler:
            return {"status": "not_initialized", "jobs": []}

        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": str(job.next_run_time) if job.next_run_time else None,
                "trigger": str(job.trigger)
            })

        return {
            "status": "running" if self.is_running else "stopped",
            "jobs": jobs
        }

#   ----------------------------------  Scheduler -----------------------------------------
