"""
Unified Data Scheduler
统一数据调度器 - 整合所有轮询系统
"""

import asyncio
import time
import threading
from datetime import datetime
from typing import Dict, List, Optional, Any
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger

from logger import logger
from unified_scheduler_config import (
    load_unified_config, 
    get_enabled_data_types,
    get_data_type_functions,
    get_data_type_schedule,
    get_scheduler_config
)

# Import existing servers
from creator_info_server import CreatorInfoServer, CreatorInfoScheduler
from user_info_till_server import vtuberUserInfoServer
from live_info_till_server import MyHandler
import aiohttp
import http.cookies

# Import cookie management
from .cookie_manager import get_cookie_manager, get_task_cookie
from .cookie_adapter import apply_user_task_cookies, apply_creator_task_cookies


class UnifiedDataScheduler:
    """
    Unified Data Scheduler - 统一数据调度器
    Integrates all polling systems (user, creator, live data)
    """

    def __init__(self, data_types: List[str] = None, uid: str = "401315430", credential=None):
        """
        Initialize unified scheduler
        
        Args:
            data_types: List of data types to enable ['user', 'creator', 'live', 'all']
            uid: User ID for creator data
            credential: Bilibili credential for API access
        """
        self.uid = uid
        self.credential = credential
        self.scheduler = None
        self.is_running = False
        
        # Load configuration
        self.config = load_unified_config()
        self.scheduler_config = get_scheduler_config()
        
        # Determine enabled data types
        if data_types is None:
            self.enabled_data_types = get_enabled_data_types()
        elif 'all' in data_types:
            self.enabled_data_types = ['user', 'creator', 'live']
        else:
            self.enabled_data_types = data_types
        
        # Initialize components
        self.creator_scheduler = None
        self.user_servers = None
        self.live_handler = None
        self.live_session = None

        # Initialize cookie manager
        self.cookie_manager = get_cookie_manager()

        logger.info(f"UnifiedDataScheduler initialized with data types: {self.enabled_data_types}")

    async def initialize(self):
        """Initialize all required components"""
        try:
            # Start cookie auto-refresh
            self.cookie_manager.start_auto_refresh()

            # Initialize creator scheduler if enabled
            if 'creator' in self.enabled_data_types:
                await self._initialize_creator_scheduler()

            # Initialize user servers if enabled
            if 'user' in self.enabled_data_types:
                await self._initialize_user_servers()

            # Initialize live monitoring if enabled
            if 'live' in self.enabled_data_types:
                await self._initialize_live_monitoring()

            logger.info("UnifiedDataScheduler initialization completed")

        except Exception as e:
            logger.error(f"Error during UnifiedDataScheduler initialization: {e}")
            raise

    async def _initialize_creator_scheduler(self):
        """Initialize creator data scheduler"""
        try:
            self.creator_scheduler = CreatorInfoScheduler(uid=self.uid, credential=self.credential)
            await self.creator_scheduler.initialize()

            # Apply creator task cookies
            if self.creator_scheduler.creator_server:
                apply_creator_task_cookies(self.creator_scheduler.creator_server)

            logger.info("Creator scheduler initialized")
        except Exception as e:
            logger.error(f"Error initializing creator scheduler: {e}")
            raise

    async def _initialize_user_servers(self):
        """Initialize user data servers"""
        try:
            self.user_servers = vtuberUserInfoServer

            # Apply user task cookies to all servers
            apply_user_task_cookies()

            # Initialize all user servers
            for vtuber, server in self.user_servers.items():
                if server.liveid is None:
                    await server.async_init()
            logger.info(f"User servers initialized for {len(self.user_servers)} vtubers")
        except Exception as e:
            logger.error(f"Error initializing user servers: {e}")
            raise

    async def _initialize_live_monitoring(self):
        """Initialize live monitoring components"""
        try:
            # Get cookie for live monitoring
            live_cookie = get_task_cookie('live')

            # Initialize session for live monitoring with cookie
            if live_cookie:
                cookies = http.cookies.SimpleCookie()
                for cookie_part in live_cookie.split('; '):
                    if '=' in cookie_part:
                        key, value = cookie_part.split('=', 1)
                        cookies[key] = value
                self.live_session = aiohttp.ClientSession(cookies=cookies)
                logger.info("Live monitoring initialized with dedicated cookie")
            else:
                self.live_session = aiohttp.ClientSession()
                logger.warning("Live monitoring initialized without cookie (disabled or not configured)")

            # Initialize live handler
            self.live_handler = MyHandler()
            logger.info("Live monitoring initialized")
        except Exception as e:
            logger.error(f"Error initializing live monitoring: {e}")
            raise

    async def execute_user_functions_safely(self, frequency: str):
        """
        Execute user data functions safely with multithreading
        安全执行用户数据函数（多线程）
        """
        if 'user' not in self.enabled_data_types:
            return
        
        functions = get_data_type_functions('user').get(frequency, [])
        if not functions:
            return
        
        logger.info(f"Starting user data collection - {frequency} frequency with {len(functions)} functions")
        
        async def execute_for_vtuber(vtuber, server):
            """Execute functions for a single vtuber"""
            try:
                if server.liveid is None:
                    await server.async_init()
                
                if not server.liveid:
                    logger.warning(f"Skipping {vtuber} - liveid not available")
                    return False
                
                success_count = 0
                for func_name in functions:
                    try:
                        func = getattr(server, func_name, None)
                        if not func:
                            logger.error(f"Function {func_name} not found for {vtuber}")
                            continue
                        
                        # Execute function with appropriate parameters
                        if func_name == "fetch_follower_review":
                            cur_date_str = datetime.now().strftime("%Y-%m-%d")
                            await func(cur_date_str, 7)
                        elif func_name in ["fetch_all_dynamics_comments", "fetch_all_videos_comments"]:
                            await func(interval_days=7)
                        else:
                            await func()
                        
                        success_count += 1
                        logger.info(f"Successfully executed {func_name} for {vtuber}")
                        
                        # Delay between functions
                        await asyncio.sleep(self.scheduler_config.get('function_delay', 5))
                        
                    except Exception as e:
                        logger.error(f"Error executing {func_name} for {vtuber}: {e}")
                
                logger.info(f"Completed {frequency} tasks for {vtuber}: {success_count}/{len(functions)} succeeded")
                return True
                
            except Exception as e:
                logger.error(f"Error in user data collection for {vtuber}: {e}")
                return False
        
        # Execute for all vtubers concurrently
        tasks = []
        for vtuber, server in self.user_servers.items():
            tasks.append(execute_for_vtuber(vtuber, server))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        success_count = sum(1 for result in results if result is True)
        
        logger.info(f"User data collection ({frequency}) completed: {success_count}/{len(tasks)} vtubers succeeded")

    async def execute_creator_functions_safely(self):
        """Execute creator data functions safely"""
        if 'creator' not in self.enabled_data_types or not self.creator_scheduler:
            return
        
        try:
            logger.info("Starting creator data collection")
            await self.creator_scheduler.execute_all_functions()
            logger.info("Creator data collection completed")
        except Exception as e:
            logger.error(f"Error in creator data collection: {e}")

    async def execute_live_monitoring(self):
        """Execute live monitoring tasks"""
        if 'live' not in self.enabled_data_types:
            return
        
        try:
            logger.info("Starting live monitoring tasks")
            # Add live monitoring logic here
            # This would typically be a continuous process
            logger.info("Live monitoring tasks completed")
        except Exception as e:
            logger.error(f"Error in live monitoring: {e}")

    def start_scheduler(self):
        """Start the unified scheduler"""
        if self.is_running:
            logger.warning("Unified scheduler is already running")
            return
        
        self.scheduler = AsyncIOScheduler(timezone=self.scheduler_config.get('timezone', 'Asia/Shanghai'))
        
        # Add jobs for each enabled data type
        self._add_user_jobs()
        self._add_creator_jobs()
        self._add_live_jobs()
        
        self.scheduler.start()
        self.is_running = True
        
        logger.info(f"UnifiedDataScheduler started with data types: {self.enabled_data_types}")

    def _add_user_jobs(self):
        """Add user data collection jobs"""
        if 'user' not in self.enabled_data_types:
            return
        
        schedule_config = get_data_type_schedule('user')
        
        # Add hourly job
        if 'hourly' in schedule_config:
            self.scheduler.add_job(
                self.execute_user_functions_safely,
                'interval',
                hours=schedule_config['hourly']['interval'],
                args=['hourly'],
                id='user_hourly_task',
                name='User Hourly Data Collection',
                next_run_time=datetime.now()
            )
        
        # Add daily job
        if 'daily' in schedule_config:
            daily_config = schedule_config['daily']
            self.scheduler.add_job(
                self.execute_user_functions_safely,
                'cron',
                hour=daily_config['hour'],
                minute=daily_config['minute'],
                args=['daily'],
                id='user_daily_task',
                name='User Daily Data Collection'
            )
        
        # Add weekly job
        if 'weekly' in schedule_config:
            weekly_config = schedule_config['weekly']
            self.scheduler.add_job(
                self.execute_user_functions_safely,
                'cron',
                day_of_week=weekly_config['day_of_week'],
                hour=weekly_config['hour'],
                minute=weekly_config['minute'],
                args=['weekly'],
                id='user_weekly_task',
                name='User Weekly Data Collection'
            )

    def _add_creator_jobs(self):
        """Add creator data collection jobs"""
        if 'creator' not in self.enabled_data_types:
            return
        
        schedule_config = get_data_type_schedule('creator')
        
        if 'daily' in schedule_config:
            daily_config = schedule_config['daily']
            self.scheduler.add_job(
                self.execute_creator_functions_safely,
                'cron',
                hour=daily_config['hour'],
                minute=daily_config['minute'],
                id='creator_daily_task',
                name='Creator Daily Data Collection'
            )

    def _add_live_jobs(self):
        """Add live monitoring jobs"""
        if 'live' not in self.enabled_data_types:
            return
        
        # Live monitoring is typically continuous
        # Add specific live monitoring jobs here if needed
        pass

    def stop_scheduler(self):
        """Stop the unified scheduler"""
        if self.scheduler and self.is_running:
            self.scheduler.shutdown()
            self.is_running = False
            logger.info("UnifiedDataScheduler stopped")
        else:
            logger.warning("Unified scheduler is not running")

        # Stop cookie auto-refresh
        if self.cookie_manager:
            self.cookie_manager.stop_auto_refresh()

    def get_scheduler_status(self):
        """Get scheduler status"""
        if not self.scheduler:
            return {"status": "not_initialized", "jobs": [], "data_types": self.enabled_data_types}
        
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": str(job.next_run_time) if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
        
        return {
            "status": "running" if self.is_running else "stopped",
            "jobs": jobs,
            "data_types": self.enabled_data_types,
            "config": self.scheduler_config
        }
