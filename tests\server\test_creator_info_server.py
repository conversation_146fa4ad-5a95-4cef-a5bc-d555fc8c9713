import asyncio
from server.creator_info_server import CreatorInfoServer
from logger import logger

cis = CreatorInfoServer()

async def main():
    # Test fetch_overview_stat
    await cis.initialize_async()
    # info = await cis.fetch_overview_stat()
    # logger.info(f"fetch_overview_stat result: {info}")

    # Test fetch_fan_graph
    # info = await cis.fetch_fan_graph()
    # logger.info(f"fetch_fan_graph result: {info}")

    # Test fetch_fan_overview
    # info = await cis.fetch_fan_overview()
    # logger.info(f"fetch_fan_overview result: {info}")

    # Test fetch_video_playanalysis
    info = await cis.fetch_video_compare(size = 1000)
    logger.info(f"fetch_video_compare result: {info}")

    # info = await cis.fetch_video_pandect()
    # logger.info(f"fetch_video_pandect result: {info}")

    # # Test fetch_video_survey
    # info = await cis.fetch_video_survey()
    # logger.info(f"fetch_video_survey result: {info}")

    # # Test fetch_video_source
    # info = await cis.fetch_video_source()
    # logger.info(f"fetch_video_source result: {info}")

    # # Test fetch_video_upload_manager_info
    # info = await cis.fetch_video_view_data()
    # logger.info(f"fetch_video_view_data result: {info}")


if __name__ == '__main__':
    asyncio.run(main())
