"""
Unified Data Scheduler Configuration
统一数据调度器配置文件
"""

import json
import os
from typing import Dict, List, Any
from logger import logger

# Default configuration for unified data scheduler
DEFAULT_CONFIG = {
    "scheduler": {
        "timezone": "Asia/Shanghai",
        "max_workers": 4,
        "function_delay": 5,  # seconds between functions
        "category_delay": 10,  # seconds between categories
        "error_retry_count": 3,
        "error_retry_delay": 30
    },
    "data_types": {
        "user": {
            "enabled": True,
            "description": "User data collection (followers, dynamics, videos)",
            "functions": {
                "minute": [
                    # Add minute-level user data functions if needed
                ],
                "hourly": [
                    "fetch_user_follower_num",
                    "fetch_dahanghai_num", 
                    "fetch_user_current_stat"
                ],
                "daily": [
                    "fetch_user_dynamics",
                    "fetch_all_video",
                    "fetch_dahanghai_list",
                    "fetch_followers_list",
                    "fetch_follower_review"
                ],
                "weekly": [
                    "fetch_fans_medal_rank",
                    "fetch_user_info"
                ],
                "monthly": [
                    "fetch_all_dynamics_comments",
                    "fetch_all_videos_comments",
                    "gen_comment_sensiment",
                    "fetch_tieba_threads",
                    "fetch_tieba_whole"
                ]
            },
            "schedule": {
                "minute": {"interval": 1},  # every minute
                "hourly": {"interval": 1},  # every hour
                "daily": {"hour": 2, "minute": 0},  # daily at 2:00 AM
                "weekly": {"day_of_week": "sat", "hour": 3, "minute": 0},  # Saturday 3:00 AM
                "monthly": {"day": 1, "hour": 4, "minute": 0}  # 1st day of month 4:00 AM
            }
        },
        "creator": {
            "enabled": True,
            "description": "Creator data collection (existing creator_info_server functions)",
            "functions": {
                "daily": [
                    # These will be loaded from creator_scheduler_config.py
                ]
            },
            "schedule": {
                "daily": {"hour": 2, "minute": 30}  # daily at 2:30 AM
            }
        },
        "live": {
            "enabled": True,
            "description": "Live streaming data collection (room status, danmaku)",
            "functions": {
                "continuous": [
                    "monitor_live_rooms",
                    "collect_danmaku_data"
                ],
                "minute": [
                    "update_live_status"
                ]
            },
            "schedule": {
                "continuous": {"mode": "continuous"},  # always running
                "minute": {"interval": 1}  # every minute
            }
        }
    }
}

def get_config_file_path():
    """Get the path to the configuration file"""
    return os.path.join(os.path.dirname(__file__), "unified_scheduler_config.json")

def load_unified_config():
    """
    Load unified scheduler configuration
    加载统一调度器配置
    """
    config_file = get_config_file_path()
    
    try:
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                logger.info(f"Loaded unified scheduler config from {config_file}")
                return config
        else:
            logger.info(f"Config file not found, creating default config at {config_file}")
            save_unified_config(DEFAULT_CONFIG)
            return DEFAULT_CONFIG.copy()
    except Exception as e:
        logger.error(f"Error loading unified scheduler config: {e}")
        return DEFAULT_CONFIG.copy()

def save_unified_config(config: Dict[str, Any]):
    """
    Save unified scheduler configuration
    保存统一调度器配置
    """
    config_file = get_config_file_path()
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        logger.info(f"Saved unified scheduler config to {config_file}")
    except Exception as e:
        logger.error(f"Error saving unified scheduler config: {e}")

def get_enabled_data_types():
    """
    Get list of enabled data types
    获取启用的数据类型列表
    """
    config = load_unified_config()
    enabled_types = []
    
    for data_type, settings in config.get("data_types", {}).items():
        if settings.get("enabled", False):
            enabled_types.append(data_type)
    
    return enabled_types

def get_data_type_functions(data_type: str):
    """
    Get functions for a specific data type
    获取指定数据类型的函数列表
    """
    config = load_unified_config()
    data_types = config.get("data_types", {})
    
    if data_type not in data_types:
        logger.error(f"Unknown data type: {data_type}")
        return {}
    
    return data_types[data_type].get("functions", {})

def get_data_type_schedule(data_type: str):
    """
    Get schedule configuration for a specific data type
    获取指定数据类型的调度配置
    """
    config = load_unified_config()
    data_types = config.get("data_types", {})
    
    if data_type not in data_types:
        logger.error(f"Unknown data type: {data_type}")
        return {}
    
    return data_types[data_type].get("schedule", {})

def get_scheduler_config():
    """
    Get general scheduler configuration
    获取通用调度器配置
    """
    config = load_unified_config()
    return config.get("scheduler", {})

def update_data_type_status(data_type: str, enabled: bool):
    """
    Update the enabled status of a data type
    更新数据类型的启用状态
    """
    config = load_unified_config()
    
    if data_type in config.get("data_types", {}):
        config["data_types"][data_type]["enabled"] = enabled
        save_unified_config(config)
        logger.info(f"Updated {data_type} enabled status to {enabled}")
    else:
        logger.error(f"Unknown data type: {data_type}")

# Initialize config file if it doesn't exist
if __name__ == "__main__":
    config = load_unified_config()
    print("Unified scheduler configuration loaded successfully")
    print(f"Enabled data types: {get_enabled_data_types()}")
