"""
Core Integration Test for Unified Data Scheduler
统一数据调度器核心集成测试
"""

import asyncio
import sys
import os

# Add server directory to path
sys.path.append(os.path.dirname(__file__))

from logger import logger


async def test_config_system():
    """Test configuration system"""
    print("=== Testing Configuration System ===")
    
    try:
        from unified_scheduler_config import (
            load_unified_config,
            get_enabled_data_types,
            get_data_type_schedule,
            get_scheduler_config
        )
        
        config = load_unified_config()
        print("✓ Configuration loaded")
        
        enabled_types = get_enabled_data_types()
        print(f"✓ Enabled data types: {enabled_types}")
        
        user_schedule = get_data_type_schedule('user')
        print(f"✓ User schedule config: {list(user_schedule.keys())}")
        
        scheduler_config = get_scheduler_config()
        print(f"✓ Scheduler config: {list(scheduler_config.keys())}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False


async def test_scheduler_class():
    """Test scheduler class creation without full initialization"""
    print("\n=== Testing Scheduler Class ===")
    
    try:
        from unified_data_scheduler import UnifiedDataScheduler, AIOLIMITER_AVAILABLE
        
        print(f"✓ aiolimiter available: {AIOLIMITER_AVAILABLE}")
        
        # Test creating instance
        scheduler = UnifiedDataScheduler(data_types=['user'], uid="401315430")
        print(f"✓ Scheduler created with data types: {scheduler.enabled_data_types}")
        
        # Test rate limiter initialization
        print(f"✓ Rate limiters initialized: {len(scheduler.limiters)} limiters")
        
        # Test task tracking
        print(f"✓ Task tracking initialized: {scheduler.active_main_tasks_count} active tasks")
        
        return True
        
    except Exception as e:
        print(f"✗ Scheduler class test failed: {e}")
        return False


async def test_task_wrapper():
    """Test task wrapper functionality"""
    print("\n=== Testing Task Wrapper ===")
    
    try:
        from unified_data_scheduler import UnifiedDataScheduler
        
        scheduler = UnifiedDataScheduler(data_types=['user'])
        
        # Test task wrapper
        async def dummy_task(task_name):
            return f"Task {task_name} completed"
        
        # Add to main tasks
        scheduler.main_task_names.add("test_task")
        
        result = await scheduler._execute_task_wrapper(dummy_task, "test_task")
        print(f"✓ Task wrapper executed: {result}")
        
        return True
        
    except Exception as e:
        print(f"✗ Task wrapper test failed: {e}")
        return False


async def test_scheduler_creation():
    """Test scheduler creation without starting"""
    print("\n=== Testing Scheduler Creation ===")
    
    try:
        from unified_data_scheduler import UnifiedDataScheduler
        from apscheduler.schedulers.asyncio import AsyncIOScheduler
        
        scheduler = UnifiedDataScheduler(data_types=['user'])
        
        # Create scheduler without starting
        scheduler.scheduler = AsyncIOScheduler(timezone="Asia/Shanghai")
        print("✓ APScheduler created")
        
        # Test adding a simple job
        async def test_job():
            return "test job executed"
        
        scheduler._add_task_to_scheduler(
            test_job,
            "test_job",
            "interval",
            seconds=60
        )
        print("✓ Test job added to scheduler")
        
        jobs = scheduler.scheduler.get_jobs()
        print(f"✓ Scheduler has {len(jobs)} jobs")
        
        return True
        
    except Exception as e:
        print(f"✗ Scheduler creation test failed: {e}")
        return False


async def test_user_job_configuration():
    """Test user job configuration without starting"""
    print("\n=== Testing User Job Configuration ===")
    
    try:
        from unified_data_scheduler import UnifiedDataScheduler
        from apscheduler.schedulers.asyncio import AsyncIOScheduler
        
        scheduler = UnifiedDataScheduler(data_types=['user'])
        scheduler.scheduler = AsyncIOScheduler(timezone="Asia/Shanghai")
        
        # Test adding user jobs
        scheduler._add_user_jobs()
        
        jobs = scheduler.scheduler.get_jobs()
        print(f"✓ User jobs added: {len(jobs)} jobs")
        
        for job in jobs:
            print(f"  - {job.name} (ID: {job.id})")
        
        return True
        
    except Exception as e:
        print(f"✗ User job configuration test failed: {e}")
        return False


async def run_core_tests():
    """Run core integration tests"""
    print("Starting Unified Data Scheduler Core Integration Tests...")
    print("=" * 70)
    
    tests = [
        ("Configuration System", test_config_system),
        ("Scheduler Class", test_scheduler_class),
        ("Task Wrapper", test_task_wrapper),
        ("Scheduler Creation", test_scheduler_creation),
        ("User Job Configuration", test_user_job_configuration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 70)
    print("Core Integration Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All core tests passed! The unified scheduler core is working.")
        print("\n📋 Summary of Integration:")
        print("✓ Configuration system integrated from unified_scheduler_config.py")
        print("✓ Rate limiters integrated from user_data_scheduler.py")
        print("✓ Task wrapper with error handling and logging")
        print("✓ Advanced scheduling with jitter and misfire grace time")
        print("✓ Support for multiple frequency types (hourly, daily, three_day, weekly, monthly, till)")
        print("✓ Idle task checking for till worker")
        print("✓ Main task tracking for concurrent execution control")
    elif passed >= len(results) * 0.7:
        print("⚠ Most core tests passed. The integration is mostly working.")
    else:
        print("❌ Many core tests failed. Please check the integration.")
    
    return passed == len(results)


if __name__ == "__main__":
    try:
        result = asyncio.run(run_core_tests())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        sys.exit(1)
