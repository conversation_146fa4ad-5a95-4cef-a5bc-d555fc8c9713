# 统一数据调度器迁移指南
# Unified Data Scheduler Migration Guide

## 概述 Overview

本文档描述如何将现有的分散轮询系统迁移到统一的数据调度器架构。
This document describes how to migrate existing scattered polling systems to the unified data scheduler architecture.

## 架构对比 Architecture Comparison

### 原有架构 Original Architecture
```
server/
├── till_main.py                    # 主轮询脚本
├── user_info_till_server.py        # 用户数据轮询
├── live_info_till_server.py        # 直播数据轮询
├── creator_info_scheduler_main.py  # 创作者调度器
└── backend/till_server.py          # 后端轮询服务
```

### 新架构 New Architecture
```
server/
├── unified_scheduler_main.py       # 统一调度器主入口
├── unified_data_scheduler.py       # 统一调度器核心
├── unified_scheduler_config.py     # 统一配置管理
└── scripts/
    ├── start_unified_scheduler.bat  # Windows启动脚本
    └── start_unified_scheduler.sh   # Linux/macOS启动脚本
```

## 功能映射 Function Mapping

### 用户数据 User Data
| 原有函数 Original Function | 新架构位置 New Location | 执行频率 Frequency |
|---------------------------|------------------------|-------------------|
| `user_info_1_minute_worker()` | `execute_user_functions_safely('minute')` | 每分钟 Every minute |
| `user_info_hourly_worker()` | `execute_user_functions_safely('hourly')` | 每小时 Every hour |
| `user_info_1_day_worker()` | `execute_user_functions_safely('daily')` | 每日 Daily |
| `user_info_week_worker()` | `execute_user_functions_safely('weekly')` | 每周 Weekly |
| `user_info_till_worker()` | `execute_user_functions_safely('monthly')` | 每月 Monthly |

### 创作者数据 Creator Data
| 原有函数 Original Function | 新架构位置 New Location |
|---------------------------|------------------------|
| `CreatorInfoScheduler` | `execute_creator_functions_safely()` |

### 直播数据 Live Data
| 原有函数 Original Function | 新架构位置 New Location |
|---------------------------|------------------------|
| `run_multi_clients()` | `execute_live_monitoring()` |

## 迁移步骤 Migration Steps

### 1. 停止现有服务 Stop Existing Services
```bash
# 停止现有的轮询服务
# Stop existing polling services
pkill -f till_main.py
pkill -f creator_info_scheduler_main.py
pkill -f live_info_till_server.py
```

### 2. 配置新系统 Configure New System
```bash
# 编辑配置文件
# Edit configuration file
python -c "from server.unified_scheduler_config import load_unified_config; print('Config loaded')"
```

### 3. 测试新系统 Test New System
```bash
# 测试运行一次
# Test run once
cd server
python unified_scheduler_main.py --mode once --data-types user

# 查看状态
# Check status
python unified_scheduler_main.py --mode status
```

### 4. 启动新系统 Start New System
```bash
# Windows
scripts\start_unified_scheduler.bat --mode monitor --data-types all

# Linux/macOS
chmod +x scripts/start_unified_scheduler.sh
scripts/start_unified_scheduler.sh --mode monitor --data-types all
```

## 使用方法 Usage

### 命令行参数 Command Line Arguments
```bash
python unified_scheduler_main.py [options]

Options:
  --mode MODE         运行模式 (monitor|once|status|help)
  --data-types TYPES  数据类型 (user,creator,live,all)
  --uid UID          用户UID
```

### 数据类型选择 Data Type Selection
- `user`: 用户数据（粉丝数、动态、视频等）
- `creator`: 创作者数据（现有creator_info_server功能）
- `live`: 直播数据（直播状态、弹幕等）
- `all`: 所有数据类型

### 运行模式 Run Modes
- `monitor`: 监控模式，持续运行并自动重启
- `once`: 执行一次，用于测试
- `status`: 显示当前状态
- `help`: 显示帮助信息

## 配置说明 Configuration

### 主配置文件 Main Configuration
配置文件位置：`server/unified_scheduler_config.json`
Configuration file location: `server/unified_scheduler_config.json`

### 配置结构 Configuration Structure
```json
{
  "scheduler": {
    "timezone": "Asia/Shanghai",
    "max_workers": 4,
    "function_delay": 5,
    "category_delay": 10
  },
  "data_types": {
    "user": {
      "enabled": true,
      "functions": {
        "hourly": ["fetch_user_follower_num", "fetch_dahanghai_num"],
        "daily": ["fetch_user_dynamics", "fetch_all_video"]
      },
      "schedule": {
        "hourly": {"interval": 1},
        "daily": {"hour": 2, "minute": 0}
      }
    }
  }
}
```

## 多线程优化 Multithreading Optimization

新架构使用asyncio.gather实现并发执行：
The new architecture uses asyncio.gather for concurrent execution:

```python
# 并发执行所有vtuber的任务
# Execute tasks for all vtubers concurrently
tasks = []
for vtuber, server in self.user_servers.items():
    tasks.append(execute_for_vtuber(vtuber, server))

results = await asyncio.gather(*tasks, return_exceptions=True)
```

## 监控和日志 Monitoring and Logging

### 日志位置 Log Location
- 主日志：`server/logs/`
- 错误日志：自动记录到主日志中

### 状态监控 Status Monitoring
```bash
# 查看运行状态
python unified_scheduler_main.py --mode status

# 查看日志
tail -f server/logs/$(date +%Y%m%d).txt
```

## 故障排除 Troubleshooting

### 常见问题 Common Issues

1. **配置文件错误 Configuration Error**
   ```bash
   # 重新生成默认配置
   python -c "from server.unified_scheduler_config import save_unified_config, DEFAULT_CONFIG; save_unified_config(DEFAULT_CONFIG)"
   ```

2. **依赖缺失 Missing Dependencies**
   ```bash
   # 检查依赖
   pip install apscheduler aiohttp psutil
   ```

3. **权限问题 Permission Issues**
   ```bash
   # Linux/macOS
   chmod +x scripts/start_unified_scheduler.sh
   ```

## 性能对比 Performance Comparison

### 原有架构 Original Architecture
- 多个独立进程
- 资源占用较高
- 难以统一管理

### 新架构 New Architecture
- 单一进程，多线程执行
- 资源占用优化
- 统一配置和监控
- 更好的错误处理和重试机制

## 回滚方案 Rollback Plan

如果需要回滚到原有架构：
If rollback to original architecture is needed:

1. 停止统一调度器
2. 重新启动原有的独立服务
3. 恢复原有的配置文件

```bash
# 停止新系统
pkill -f unified_scheduler_main.py

# 启动原有系统
python till_main.py &
python creator_info_scheduler_main.py &
python live_info_till_server.py &
```
