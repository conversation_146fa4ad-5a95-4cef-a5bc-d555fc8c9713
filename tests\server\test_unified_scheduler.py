"""
Test script for Unified Data Scheduler
统一数据调度器测试脚本
"""

import asyncio
import sys
import os

# Add server directory to path
sys.path.append(os.path.dirname(__file__))

from logger import logger
from server.unified_scheduler_config import (
    load_unified_config, 
    get_enabled_data_types,
    get_data_type_functions,
    save_unified_config,
    DEFAULT_CONFIG
)
from server.unified_data_scheduler import UnifiedDataScheduler


async def test_config_loading():
    """Test configuration loading"""
    print("=== Testing Configuration Loading ===")
    
    try:
        # Test loading config
        config = load_unified_config()
        print(f"✓ Configuration loaded successfully")
        print(f"  Enabled data types: {get_enabled_data_types()}")
        
        # Test getting functions for each data type
        for data_type in ['user', 'creator', 'live']:
            functions = get_data_type_functions(data_type)
            print(f"  {data_type} functions: {len(functions)} categories")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration loading failed: {e}")
        return False


async def test_scheduler_initialization():
    """Test scheduler initialization"""
    print("\n=== Testing Scheduler Initialization ===")
    
    try:
        # Test with user data type only (safer for testing)
        scheduler = UnifiedDataScheduler(data_types=['user'], uid="401315430")
        print(f"✓ Scheduler created with data types: {scheduler.enabled_data_types}")
        
        # Test initialization (this might fail if dependencies are missing)
        try:
            await scheduler.initialize()
            print("✓ Scheduler initialized successfully")
            
            # Test status
            status = scheduler.get_scheduler_status()
            print(f"✓ Status retrieved: {status['status']}")
            
            return True
            
        except Exception as e:
            print(f"⚠ Scheduler initialization failed (expected if dependencies missing): {e}")
            return False
        
    except Exception as e:
        print(f"✗ Scheduler creation failed: {e}")
        return False


async def test_scheduler_jobs():
    """Test scheduler job creation"""
    print("\n=== Testing Scheduler Jobs ===")
    
    try:
        scheduler = UnifiedDataScheduler(data_types=['user'], uid="401315430")
        
        # Create scheduler without full initialization
        scheduler.scheduler = None
        scheduler.is_running = False
        
        # Test job creation methods
        scheduler.start_scheduler()
        
        if scheduler.scheduler:
            jobs = scheduler.scheduler.get_jobs()
            print(f"✓ Scheduler started with {len(jobs)} jobs")
            
            for job in jobs:
                print(f"  - {job.name} (ID: {job.id})")
            
            scheduler.stop_scheduler()
            print("✓ Scheduler stopped successfully")
            
            return True
        else:
            print("✗ Scheduler not created")
            return False
        
    except Exception as e:
        print(f"✗ Scheduler jobs test failed: {e}")
        return False


def test_config_management():
    """Test configuration management"""
    print("\n=== Testing Configuration Management ===")
    
    try:
        # Test saving and loading config
        test_config = DEFAULT_CONFIG.copy()
        test_config['test_flag'] = True
        
        save_unified_config(test_config)
        print("✓ Configuration saved successfully")
        
        loaded_config = load_unified_config()
        if loaded_config.get('test_flag'):
            print("✓ Configuration loaded and verified")
        else:
            print("⚠ Configuration loaded but test flag missing")
        
        # Clean up test flag
        del loaded_config['test_flag']
        save_unified_config(loaded_config)
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration management test failed: {e}")
        return False


def test_import_dependencies():
    """Test importing required dependencies"""
    print("\n=== Testing Dependencies ===")
    
    dependencies = [
        ('apscheduler', 'AsyncIOScheduler'),
        ('aiohttp', 'ClientSession'),
        ('logger', 'logger'),
    ]
    
    success_count = 0
    
    for module_name, class_name in dependencies:
        try:
            if module_name == 'apscheduler':
                from apscheduler.schedulers.asyncio import AsyncIOScheduler
            elif module_name == 'aiohttp':
                import aiohttp
            elif module_name == 'logger':
                from logger import logger
            
            print(f"✓ {module_name} imported successfully")
            success_count += 1
            
        except ImportError as e:
            print(f"✗ {module_name} import failed: {e}")
    
    print(f"Dependencies: {success_count}/{len(dependencies)} available")
    return success_count == len(dependencies)


async def run_all_tests():
    """Run all tests"""
    print("Starting Unified Data Scheduler Tests...")
    print("=" * 50)
    
    tests = [
        ("Dependencies", test_import_dependencies),
        ("Configuration Loading", test_config_loading),
        ("Configuration Management", test_config_management),
        ("Scheduler Initialization", test_scheduler_initialization),
        ("Scheduler Jobs", test_scheduler_jobs),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! The unified scheduler is ready to use.")
    elif passed >= len(results) * 0.7:
        print("⚠ Most tests passed. Some features may not work due to missing dependencies.")
    else:
        print("❌ Many tests failed. Please check the setup and dependencies.")
    
    return passed == len(results)


if __name__ == "__main__":
    try:
        result = asyncio.run(run_all_tests())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        sys.exit(1)
