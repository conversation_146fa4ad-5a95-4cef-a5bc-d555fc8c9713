#!/usr/bin/env python3
"""
Verification script for pubtime column changes.

This script verifies that:
1. The SQL statements are syntactically correct
2. The parameter count matches between creator_info_server.py and creator_sql.py
3. The column order is correct

Usage:
    python fix/verify_pubtime_changes.py
"""

import re
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def verify_sql_syntax():
    """Verify SQL syntax by checking basic structure."""
    print("Verifying SQL syntax...")
    
    # Read the SQL file
    with open('sql/creator_sql.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if pubtime column exists in CREATE TABLE
    create_table_match = re.search(
        r'create_video_compare_table_sql = """CREATE TABLE.*?pubtime bigint.*?"""',
        content,
        re.DOTALL
    )
    
    if not create_table_match:
        print("❌ ERROR: pubtime column not found in CREATE TABLE statement")
        return False
    else:
        print("✅ pubtime column found in CREATE TABLE statement")
    
    # Check if pubtime is in INSERT statement columns
    insert_match = re.search(
        r'insert_video_compare_table_sql = """INSERT INTO video_compare_table \((.*?)\)',
        content,
        re.DOTALL
    )
    
    if insert_match:
        columns = insert_match.group(1)
        if 'pubtime' in columns:
            print("✅ pubtime column found in INSERT statement columns")
        else:
            print("❌ ERROR: pubtime column not found in INSERT statement columns")
            return False
    else:
        print("❌ ERROR: Could not parse INSERT statement")
        return False
    
    # Check if pubtime is in ON CONFLICT UPDATE
    conflict_match = re.search(
        r'ON CONFLICT.*?DO UPDATE SET(.*?)""";',
        content,
        re.DOTALL
    )
    
    if conflict_match:
        update_clause = conflict_match.group(1)
        if 'pubtime = EXCLUDED.pubtime' in update_clause:
            print("✅ pubtime found in ON CONFLICT UPDATE clause")
        else:
            print("❌ ERROR: pubtime not found in ON CONFLICT UPDATE clause")
            return False
    else:
        print("❌ ERROR: Could not parse ON CONFLICT clause")
        return False
    
    return True


def verify_parameter_count():
    """Verify parameter count matches between server and SQL."""
    print("\nVerifying parameter count...")
    
    # Read creator_info_server.py
    with open('server/creator_info_server.py', 'r', encoding='utf-8') as f:
        server_content = f.read()
    
    # Read creator_sql.py
    with open('sql/creator_sql.py', 'r', encoding='utf-8') as f:
        sql_content = f.read()
    
    # Count parameters in server record tuple
    record_match = re.search(
        r'record = \((.*?)\)',
        server_content,
        re.DOTALL
    )

    if record_match:
        record_content = record_match.group(1)
        # Count non-empty lines (parameters) - filter out empty strings
        params = [line.strip() for line in record_content.split(',') if line.strip()]
        server_params = len(params)
        print(f"Server record parameters: {server_params}")
        print(f"Parameters found: {params[:3]}...{params[-3:] if len(params) > 6 else params[3:]}")
    else:
        print("❌ ERROR: Could not find record tuple in server file")
        return False
    
    # Count placeholders in SQL - only in the insert statement
    insert_sql_match = re.search(
        r'insert_video_compare_table_sql = """INSERT INTO video_compare_table.*?VALUES \((.*?)\)',
        sql_content,
        re.DOTALL
    )

    if insert_sql_match:
        values_content = insert_sql_match.group(1)
        sql_placeholders = values_content.count('$')
        print(f"SQL placeholders: {sql_placeholders}")
    else:
        print("❌ ERROR: Could not find VALUES clause in INSERT statement")
        return False
    
    if server_params == sql_placeholders:
        print("✅ Parameter count matches")
        return True
    else:
        print(f"❌ ERROR: Parameter count mismatch - Server: {server_params}, SQL: {sql_placeholders}")
        return False


def verify_column_order():
    """Verify column order matches between server and SQL."""
    print("\nVerifying column order...")
    
    # Expected order based on the server record
    expected_order = [
        'uid', 'aid', 'bvid', 'cover', 'title', 'duration', 
        'stat', 'is_only_self', 'hour_stat', 'pubtime', 
        'create_time', 'update_time'
    ]
    
    # Read SQL file and extract column order
    with open('sql/creator_sql.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    insert_match = re.search(
        r'INSERT INTO video_compare_table \((.*?)\)',
        content,
        re.DOTALL
    )
    
    if insert_match:
        columns_text = insert_match.group(1)
        # Clean and split columns
        actual_columns = [col.strip() for col in columns_text.replace('\n', '').split(',') if col.strip()]
        
        print(f"Expected order: {expected_order}")
        print(f"Actual order:   {actual_columns}")
        
        if actual_columns == expected_order:
            print("✅ Column order matches")
            return True
        else:
            print("❌ ERROR: Column order mismatch")
            return False
    else:
        print("❌ ERROR: Could not parse INSERT columns")
        return False


def main():
    """Main verification function."""
    print("Starting verification of pubtime column changes...\n")
    
    success = True
    
    # Run all verifications
    success &= verify_sql_syntax()
    success &= verify_parameter_count()
    success &= verify_column_order()
    
    print("\n" + "="*50)
    if success:
        print("✅ All verifications passed! Changes look correct.")
        print("\nNext steps:")
        print("1. Run the migration script: python fix/add_pubtime_column.py")
        print("2. Test the creator_info_server functionality")
        print("3. Verify API endpoints return pubtime data")
    else:
        print("❌ Some verifications failed. Please review the changes.")
        sys.exit(1)


if __name__ == "__main__":
    main()
