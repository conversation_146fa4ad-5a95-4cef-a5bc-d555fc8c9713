import asyncio
from datetime import datetime, timedelta

from aiolimiter import AsyncLimiter
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger

from logger import logger
from server.user_info_till_server import (
    user_info_1_day_worker,
    user_info_3_day_worker,
    user_info_hour_worker,
    user_info_month_worker,
    user_info_till_worker,
    user_info_week_worker,
)


class AsyncScheduler:
    def __init__(self, max_concurrent_tasks=None):
        """
        初始化异步调度器。
        :param max_concurrent_tasks: 最大并发任务数，None表示不限制 (apscheduler默认行为)。
                                     可以考虑使用 Semaphore 来限制并发。
        """
        self.scheduler = AsyncIOScheduler(timezone="Asia/Shanghai")
        self.active_main_tasks_count = 0  # 用于计数当前正在运行的主要任务数量
        self.main_task_names = set()  # 用于存储所有主要任务的名称

    async def _execute_task_wrapper(self, task_func, task_name: str, *args, **kwargs):
        """
        包装任务执行，包含错误处理和日志记录。
        这个包装器由 apscheduler 调用。
        """
        is_main_task = task_name in self.main_task_names
        if is_main_task:
            self.active_main_tasks_count += 1
            logger.info(
                f"调度器触发主要任务: {task_name}，当前活动主要任务数: {self.active_main_tasks_count}"
            )
        else:
            logger.info(f"调度器触发任务: {task_name}")

        try:
            result = await task_func(task_name, *args, **kwargs)
            logger.info(f"任务 {task_name} 成功完成。结果: {result}")
        except asyncio.CancelledError:
            logger.warning(f"任务 {task_name} 被取消。")
        except Exception as e:
            logger.error(f"任务 {task_name} 执行时发生未捕获的异常: {e}", exc_info=True)
        finally:
            if is_main_task:
                self.active_main_tasks_count -= 1
                logger.info(
                    f"主要任务 {task_name} 完成，当前活动主要任务数: {self.active_main_tasks_count}"
                )

    def add_task(self, task_func, task_name: str, trigger_type: str, **trigger_args):
        """
        添加任务到调度器。

        :param task_func: 要执行的异步函数。
        :param task_name: 任务的名称，用于日志和跟踪。
        :param trigger_type: 触发器类型 ('cron' 或 'interval').
        :param trigger_args: 触发器的参数。
        """
        # 将非空闲时间补充任务视为主要任务
        if task_name != "空闲时间补充任务":
            self.main_task_names.add(task_name)

        if trigger_type == "cron":
            # 对于cron任务，可以指定 jitter 来增加随机延迟，避免同时触发大量任务
            jitter_value = trigger_args.pop("jitter", None)
            trigger = CronTrigger(
                **trigger_args,
                timezone="Asia/Shanghai",
                jitter=jitter_value,
            )
        elif trigger_type == "interval":
            jitter_value = trigger_args.pop("jitter", None)
            trigger = IntervalTrigger(
                **trigger_args,
                timezone="Asia/Shanghai",
                jitter=jitter_value,
            )
        else:
            logger.error(
                f"不支持的触发器类型: {trigger_type}，任务 {task_name} 添加失败。"
            )
            return

        self.scheduler.add_job(
            self._execute_task_wrapper,  # 使用包装器
            trigger=trigger,
            args=[task_func, task_name],  # 传递给包装器的参数
            id=task_name,
            name=task_name,
            replace_existing=True,
            misfire_grace_time=300,  # 允许任务错过执行时间5分钟，之后尝试执行
            # max_instances: 可以限制同一个job的并发实例数量，默认为1。
            next_run_time=datetime.now() + timedelta(seconds=1),  # 立即开始任务
        )
        logger.info(
            f"任务 {task_name} 已添加到调度器。触发器: {trigger_type}, 参数: {trigger_args}"
        )

    def get_active_main_tasks_count(self):
        """
        获取当前正在运行的主要任务数量。
        """
        return self.active_main_tasks_count

    async def run_idle_task_if_needed(self, task_name: str):
        """
        如果调度器没有正在运行的主要任务，则运行user_info_till_worker。
        使用独立的并发限制器，避免阻塞其他任务。
        """
        active_count = self.get_active_main_tasks_count()
        if active_count == 0:
            logger.info(f"调度器空闲，正在执行 {task_name} (user_info_till_worker)。")
            try:
                async with till_worker_limiter:
                    await user_info_till_worker()
                    logger.info(f"{task_name} (user_info_till_worker) 执行成功。")
            except Exception as e:
                logger.error(
                    f"{task_name} (user_info_till_worker) 执行时发生异常: {e}",
                    exc_info=True,
                )
        else:
            logger.info(
                f"调度器有 {active_count} 个主要任务正在运行，跳过执行 {task_name}。"
            )

    def start(self):
        if not self.scheduler.running:
            try:
                self.scheduler.start()
                logger.info("调度器已启动。")
            except Exception as e:
                logger.error(f"启动调度器失败: {e}", exc_info=True)
        else:
            logger.info("调度器已在运行中。")

    def shutdown(self, wait=True):
        if self.scheduler.running:
            try:
                self.scheduler.shutdown(wait=wait)
                logger.info("调度器已关闭。")
            except Exception as e:
                logger.error(f"关闭调度器失败: {e}", exc_info=True)
        else:
            logger.info("调度器未运行。")


# 为不同类型的任务定义独立的限制器
# 允许1个每小时任务并发执行
hourly_task_limiter = AsyncLimiter(1, 1)
daily_task_limiter = AsyncLimiter(1, 1)
# 允许2个其他长周期任务（每日、每三日、每周、每月）并发执行
long_running_task_limiter = AsyncLimiter(1, 1)
till_worker_limiter = AsyncLimiter(1, 1)  # 限制为1个并发实例


async def hour_work(task_name: str):
    async with hourly_task_limiter:
        logger.info(f"小时任务限流器允许任务: {task_name} 执行")
        return await user_info_hour_worker()


async def one_day_work(task_name: str):
    async with long_running_task_limiter:
        logger.info(f"日任务限流器允许任务: {task_name} 执行")
        return await user_info_1_day_worker()


async def three_day_work(task_name: str):
    async with long_running_task_limiter:
        logger.info(f"长周期任务限流器允许任务: {task_name} 执行")
        return await user_info_3_day_worker()


async def week_work(task_name: str):
    async with long_running_task_limiter:
        logger.info(f"长周期任务限流器允许任务: {task_name} 执行")
        return await user_info_week_worker()


async def month_work(task_name: str):
    async with long_running_task_limiter:
        logger.info(f"长周期任务限流器允许任务: {task_name} 执行")
        return await user_info_month_worker()


async def main():
    scheduler_manager = AsyncScheduler()

    # 每小时任务
    scheduler_manager.add_task(hour_work, "每小时任务", "cron", minute=0, jitter=60)

    # 每日任务
    scheduler_manager.add_task(
        one_day_work, "每日任务", "cron", hour=0, minute=0, jitter=120
    )

    # 每三日任务
    scheduler_manager.add_task(
        three_day_work, "每三日任务", "cron", day="*/3", hour=0, minute=0, jitter=120
    )

    # 每周任务 (例如：每周一的凌晨4点执行)
    scheduler_manager.add_task(
        week_work,
        "每周任务",
        "cron",
        day_of_week="mon",
        hour=3,
        minute=0,
        jitter=300,
    )

    # 每月任务 (例如：每月1号的凌晨5点执行)
    scheduler_manager.add_task(
        month_work, "每月任务", "cron", day=1, hour=0, minute=0, jitter=600
    )

    # 空闲时间补充任务：每5分钟检查一次，如果调度器空闲则运行 user_info_till_worker
    scheduler_manager.add_task(
        scheduler_manager.run_idle_task_if_needed,
        "空闲时间补充任务",
        "interval",
        minutes=5,
        jitter=30,
    )

    scheduler_manager.start()

    try:
        while True:
            await asyncio.sleep(3600)  # 每小时检查一次，或者根据需要调整
    except (KeyboardInterrupt, SystemExit):
        logger.info("收到退出信号，正在关闭调度器...")
    finally:
        scheduler_manager.shutdown()
        logger.info("程序已退出。")


if __name__ == "__main__":
    asyncio.run(main())
